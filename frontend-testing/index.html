<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Backend API - Testing Interface</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>Secure Backend API</h1>
                    <span class="subtitle">Testing Interface</span>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <h2>Welcome to the Secure Backend Testing Interface</h2>
                <p>This comprehensive testing interface allows you to interact with all backend API features including authentication, MFA, session management, user administration, and security monitoring.</p>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="quickLogin()">
                        <i class="fas fa-sign-in-alt"></i>
                        Quick Login
                    </button>
                    <button class="btn btn-secondary" onclick="checkServerHealth()">
                        <i class="fas fa-heartbeat"></i>
                        Check Server Health
                    </button>
                    <button class="btn btn-info" onclick="viewApiDocs()">
                        <i class="fas fa-book"></i>
                        API Documentation
                    </button>
                </div>
            </section>

            <!-- Feature Categories -->
            <section class="features-grid">
                <h2>API Feature Categories</h2>
                <div class="grid">
                    <!-- Authentication -->
                    <div class="feature-card" data-category="authentication">
                        <div class="card-header">
                            <i class="fas fa-key"></i>
                            <h3>Authentication</h3>
                        </div>
                        <div class="card-content">
                            <p>Test user registration, login, password reset, email verification, and profile management.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    15+ Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-shield-alt"></i>
                                    JWT + OAuth
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('authentication')">
                                <i class="fas fa-arrow-right"></i>
                                Test Authentication
                            </button>
                        </div>
                    </div>

                    <!-- Multi-Factor Authentication -->
                    <div class="feature-card" data-category="mfa">
                        <div class="card-header">
                            <i class="fas fa-mobile-alt"></i>
                            <h3>Multi-Factor Authentication</h3>
                        </div>
                        <div class="card-content">
                            <p>Test TOTP setup, QR code generation, backup codes, and MFA verification flows.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    8 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-qrcode"></i>
                                    TOTP + Backup
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('mfa')">
                                <i class="fas fa-arrow-right"></i>
                                Test MFA
                            </button>
                        </div>
                    </div>

                    <!-- Session Management -->
                    <div class="feature-card" data-category="sessions">
                        <div class="card-header">
                            <i class="fas fa-desktop"></i>
                            <h3>Session Management</h3>
                        </div>
                        <div class="card-content">
                            <p>Test device fingerprinting, session tracking, remote termination, and activity monitoring.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    7 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-fingerprint"></i>
                                    Device Tracking
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('sessions')">
                                <i class="fas fa-arrow-right"></i>
                                Test Sessions
                            </button>
                        </div>
                    </div>

                    <!-- User Management -->
                    <div class="feature-card" data-category="user-management">
                        <div class="card-header">
                            <i class="fas fa-users-cog"></i>
                            <h3>User Management</h3>
                        </div>
                        <div class="card-content">
                            <p>Test admin functions including user listing, role management, and user administration.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    10+ Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-user-shield"></i>
                                    Admin Only
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('user-management')">
                                <i class="fas fa-arrow-right"></i>
                                Test User Mgmt
                            </button>
                        </div>
                    </div>

                    <!-- Security Features -->
                    <div class="feature-card" data-category="security">
                        <div class="card-header">
                            <i class="fas fa-shield-virus"></i>
                            <h3>Security Features</h3>
                        </div>
                        <div class="card-content">
                            <p>Test rate limiting, DDoS protection, threat monitoring, and security analytics.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    5+ Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-chart-line"></i>
                                    Real-time
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('security')">
                                <i class="fas fa-arrow-right"></i>
                                Test Security
                            </button>
                        </div>
                    </div>

                    <!-- OAuth Integration -->
                    <div class="feature-card" data-category="oauth">
                        <div class="card-header">
                            <i class="fab fa-google"></i>
                            <h3>OAuth Integration</h3>
                        </div>
                        <div class="card-content">
                            <p>Test OAuth flows with Google, Facebook, and GitHub authentication providers.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    8 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-link"></i>
                                    3 Providers
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('oauth')">
                                <i class="fas fa-arrow-right"></i>
                                Test OAuth
                            </button>
                        </div>
                    </div>

                    <!-- Email Verification -->
                    <div class="feature-card" data-category="email-verification">
                        <div class="card-header">
                            <i class="fas fa-envelope-circle-check"></i>
                            <h3>Email Verification</h3>
                        </div>
                        <div class="card-content">
                            <p>Test email verification requests and token validation processes.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-plug"></i>
                                    2 Endpoints
                                </span>
                                <span class="stat">
                                    <i class="fas fa-envelope"></i>
                                    Email Flow
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('email-verification')">
                                <i class="fas fa-arrow-right"></i>
                                Test Email
                            </button>
                        </div>
                    </div>

                    <!-- API Testing Tools -->
                    <div class="feature-card" data-category="tools">
                        <div class="card-header">
                            <i class="fas fa-tools"></i>
                            <h3>Testing Tools</h3>
                        </div>
                        <div class="card-content">
                            <p>Advanced testing utilities, request logging, and API exploration tools.</p>
                            <div class="feature-stats">
                                <span class="stat">
                                    <i class="fas fa-bug"></i>
                                    Debug Tools
                                </span>
                                <span class="stat">
                                    <i class="fas fa-history"></i>
                                    Request Log
                                </span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="navigateToFeature('tools')">
                                <i class="fas fa-arrow-right"></i>
                                Open Tools
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="recent-activity">
                <h2>Recent API Activity</h2>
                <div class="activity-log" id="activityLog">
                    <div class="activity-item">
                        <i class="fas fa-info-circle"></i>
                        <span class="activity-text">Welcome! Start by checking server health or testing authentication.</span>
                        <span class="activity-time">Just now</span>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 Secure Backend API Testing Interface</p>
                    <p>Built for comprehensive API testing and development</p>
                </div>
                <div class="footer-links">
                    <a href="docs/README.md" target="_blank">
                        <i class="fas fa-book"></i>
                        Documentation
                    </a>
                    <a href="http://localhost:3000/api-docs" target="_blank">
                        <i class="fas fa-code"></i>
                        API Docs
                    </a>
                    <a href="http://localhost:3000/health" target="_blank">
                        <i class="fas fa-heartbeat"></i>
                        Health Check
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="assets/js/utils/api.js"></script>
    <script src="assets/js/utils/auth.js"></script>
    <script src="assets/js/utils/storage.js"></script>
    <script src="assets/js/utils/notifications.js"></script>
    <script src="assets/js/shared/components.js"></script>
    <script src="assets/js/shared/constants.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load recent activity
            loadRecentActivity();
        }

        function navigateToFeature(feature) {
            window.location.href = `features/${feature}/index.html`;
        }

        function quickLogin() {
            window.location.href = 'features/authentication/login/index.html';
        }

        function viewApiDocs() {
            window.open('http://localhost:3000/api-docs', '_blank');
        }

        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();

                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                    addActivityLog('Server health check successful', 'success');
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
                addActivityLog('Server health check failed', 'error');
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function loadRecentActivity() {
            const savedActivity = JSON.parse(localStorage.getItem('recentActivity') || '[]');
            const activityLog = document.getElementById('activityLog');

            // Clear existing content except welcome message
            const welcomeMessage = activityLog.querySelector('.activity-item');
            activityLog.innerHTML = '';
            activityLog.appendChild(welcomeMessage);

            // Add saved activities
            savedActivity.slice(-5).forEach(activity => {
                addActivityLogElement(activity.message, activity.type, activity.timestamp);
            });
        }

        function addActivityLog(message, type = 'info') {
            const activity = {
                message,
                type,
                timestamp: new Date().toISOString()
            };

            // Save to localStorage
            const savedActivity = JSON.parse(localStorage.getItem('recentActivity') || '[]');
            savedActivity.push(activity);

            // Keep only last 50 activities
            if (savedActivity.length > 50) {
                savedActivity.shift();
            }

            localStorage.setItem('recentActivity', JSON.stringify(savedActivity));

            // Add to UI
            addActivityLogElement(message, type);
        }

        function addActivityLogElement(message, type, timestamp) {
            const activityLog = document.getElementById('activityLog');
            const activityItem = document.createElement('div');
            activityItem.className = `activity-item activity-${type}`;

            const icon = type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-circle' :
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle';

            const timeText = timestamp ?
                new Date(timestamp).toLocaleTimeString() :
                'Just now';

            activityItem.innerHTML = `
                <i class="fas ${icon}"></i>
                <span class="activity-text">${message}</span>
                <span class="activity-time">${timeText}</span>
            `;

            // Insert at the beginning (after welcome message if it exists)
            const firstChild = activityLog.firstChild;
            if (firstChild && firstChild.textContent.includes('Welcome!')) {
                activityLog.insertBefore(activityItem, firstChild.nextSibling);
            } else {
                activityLog.insertBefore(activityItem, firstChild);
            }
        }
    </script>
</body>
</html>