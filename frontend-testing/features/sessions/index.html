<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Management Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .session-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .session-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .session-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .session-tab:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .session-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .session-table th,
        .session-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .session-table th {
            background-color: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .session-table tr:hover {
            background-color: var(--bg-secondary);
        }

        .session-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .session-status.active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .session-status.expired {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
        }

        .session-status.current {
            background-color: rgba(37, 99, 235, 0.1);
            color: var(--primary-color);
        }

        .device-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .device-icon {
            font-size: 1rem;
            color: var(--text-secondary);
        }

        .location-info {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .session-actions {
            display: flex;
            gap: 0.5rem;
        }

        .activity-timeline {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            font-size: 1rem;
            color: var(--primary-color);
            margin-top: 0.125rem;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }

        .activity-description {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .trusted-device {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .trusted-device.current {
            border-color: var(--primary-color);
            background-color: rgba(37, 99, 235, 0.05);
        }

        .device-details {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .device-meta {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .refresh-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }

        .auto-refresh {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .response-viewer {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .quick-test-buttons {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .session-tabs {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .session-table {
                font-size: 0.875rem;
            }

            .session-actions {
                flex-direction: column;
            }

            .trusted-device {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../../index.html" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-arrow-left"></i>
                        <i class="fas fa-desktop"></i>
                        <div>
                            <h1>Session Management</h1>
                            <span class="subtitle">Device Tracking, Session Control & Activity Monitoring</span>
                        </div>
                    </a>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Session Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3>Session Overview</h3>
                    <p>Current session information and quick actions</p>
                </div>
                <div class="card-body">
                    <div class="refresh-indicator">
                        <i class="fas fa-sync auto-refresh"></i>
                        <span>Auto-refreshing every 30 seconds</span>
                        <button class="btn btn-sm btn-outline ml-2" onclick="toggleAutoRefresh()">
                            <span id="autoRefreshText">Disable</span>
                        </button>
                    </div>

                    <div class="quick-test-buttons">
                        <button class="btn btn-sm btn-primary" onclick="loadCurrentSession()">
                            <i class="fas fa-sync"></i>
                            Refresh Current
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="loadAllSessions()">
                            <i class="fas fa-list"></i>
                            Load All Sessions
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="terminateAllOthers()">
                            <i class="fas fa-sign-out-alt"></i>
                            Terminate Others
                        </button>
                        <button class="btn btn-sm btn-success" onclick="trustCurrentDevice()">
                            <i class="fas fa-shield-check"></i>
                            Trust Device
                        </button>
                    </div>

                    <div id="currentSessionInfo" class="mt-3">
                        <!-- Current session info will be displayed here -->
                    </div>
                </div>
            </div>

            <!-- Session Tabs -->
            <div class="session-tabs">
                <button class="session-tab active" data-tab="sessions">
                    <i class="fas fa-list"></i>
                    Active Sessions
                </button>
                <button class="session-tab" data-tab="current">
                    <i class="fas fa-desktop"></i>
                    Current Session
                </button>
                <button class="session-tab" data-tab="devices">
                    <i class="fas fa-mobile-alt"></i>
                    Trusted Devices
                </button>
                <button class="session-tab" data-tab="activity">
                    <i class="fas fa-history"></i>
                    Activity Log
                </button>
                <button class="session-tab" data-tab="control">
                    <i class="fas fa-cog"></i>
                    Session Control
                </button>
            </div>

            <!-- Active Sessions Tab -->
            <div class="tab-content active" id="sessions-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Active Sessions</h3>
                        <p>All active sessions for your account</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadAllSessions()">
                                <i class="fas fa-sync"></i>
                                Refresh Sessions
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateDemoSessions()">
                                <i class="fas fa-play"></i>
                                Demo Data
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearSessionsTable()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div id="sessionsTableContainer">
                            <table class="session-table">
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Device</th>
                                        <th>Location</th>
                                        <th>IP Address</th>
                                        <th>Last Activity</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="sessionsTableBody">
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <i class="fas fa-info-circle"></i>
                                            Click "Refresh Sessions" to load active sessions
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div id="sessionsResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Current Session Tab -->
            <div class="tab-content" id="current-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Current Session Details</h3>
                        <p>Detailed information about your current session</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadCurrentSession()">
                                <i class="fas fa-sync"></i>
                                Refresh Current
                            </button>
                            <button class="btn btn-sm btn-success" onclick="trustCurrentDevice()">
                                <i class="fas fa-shield-check"></i>
                                Trust This Device
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateCurrentSessionDemo()">
                                <i class="fas fa-play"></i>
                                Demo Data
                            </button>
                        </div>

                        <div id="currentSessionDetails">
                            <!-- Current session details will be displayed here -->
                        </div>

                        <div id="currentResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Trusted Devices Tab -->
            <div class="tab-content" id="devices-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Trusted Devices</h3>
                        <p>Manage devices that are trusted for your account</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Info:</strong> Trusted devices can skip certain security checks and may have extended session durations.
                        </div>

                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadTrustedDevices()">
                                <i class="fas fa-sync"></i>
                                Refresh Devices
                            </button>
                            <button class="btn btn-sm btn-success" onclick="trustCurrentDevice()">
                                <i class="fas fa-plus"></i>
                                Trust Current Device
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateTrustedDevicesDemo()">
                                <i class="fas fa-play"></i>
                                Demo Data
                            </button>
                        </div>

                        <div id="trustedDevicesList">
                            <!-- Trusted devices will be displayed here -->
                        </div>

                        <div id="devicesResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Activity Log Tab -->
            <div class="tab-content" id="activity-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Session Activity Log</h3>
                        <p>Timeline of session-related activities and events</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadSessionActivity()">
                                <i class="fas fa-sync"></i>
                                Refresh Activity
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="loadSessionActivity(50)">
                                <i class="fas fa-list"></i>
                                Load More (50)
                            </button>
                            <button class="btn btn-sm btn-info" onclick="generateActivityDemo()">
                                <i class="fas fa-play"></i>
                                Demo Data
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="clearActivityLog()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <div class="activity-timeline" id="activityTimeline">
                            <div class="activity-item">
                                <i class="fas fa-info-circle activity-icon"></i>
                                <div class="activity-content">
                                    <div class="activity-title">No activity loaded</div>
                                    <div class="activity-description">Click "Refresh Activity" to load session activity</div>
                                    <div class="activity-time">Just now</div>
                                </div>
                            </div>
                        </div>

                        <div id="activityResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Session Control Tab -->
            <div class="tab-content" id="control-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Session Control</h3>
                        <p>Terminate sessions and manage session security</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Warning:</strong> Terminating sessions will log out users from those devices immediately.
                        </div>

                        <!-- Terminate Specific Session -->
                        <div class="mb-4">
                            <h4>Terminate Specific Session</h4>
                            <form id="terminateSessionForm" class="mfa-form" style="max-width: 500px;">
                                <div class="form-group">
                                    <label class="form-label" for="sessionIdInput">Session ID</label>
                                    <input type="text" id="sessionIdInput" name="sessionId" class="form-input"
                                           placeholder="Enter session ID to terminate" required>
                                    <small class="text-muted">Get session IDs from the Active Sessions tab</small>
                                </div>

                                <button type="submit" class="btn btn-warning" style="width: 100%;">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Terminate Session
                                </button>
                            </form>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="mb-4">
                            <h4>Bulk Session Actions</h4>
                            <div class="quick-test-buttons">
                                <button class="btn btn-warning" onclick="terminateAllOthers()">
                                    <i class="fas fa-sign-out-alt"></i>
                                    Terminate All Other Sessions
                                </button>
                                <button class="btn btn-error" onclick="confirmTerminateAll()">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Terminate ALL Sessions
                                </button>
                            </div>
                            <small class="text-muted">
                                "Terminate All Other Sessions" keeps your current session active.<br>
                                "Terminate ALL Sessions" will log you out completely.
                            </small>
                        </div>

                        <!-- Session Security Settings -->
                        <div class="mb-4">
                            <h4>Session Security</h4>
                            <div class="quick-test-buttons">
                                <button class="btn btn-success" onclick="trustCurrentDevice()">
                                    <i class="fas fa-shield-check"></i>
                                    Trust Current Device
                                </button>
                                <button class="btn btn-info" onclick="getDeviceFingerprint()">
                                    <i class="fas fa-fingerprint"></i>
                                    Show Device Fingerprint
                                </button>
                                <button class="btn btn-secondary" onclick="checkSessionSecurity()">
                                    <i class="fas fa-shield-alt"></i>
                                    Security Check
                                </button>
                            </div>
                        </div>

                        <!-- Test Session Actions -->
                        <div class="mb-4">
                            <h4>Test Session Actions</h4>
                            <div class="quick-test-buttons">
                                <button class="btn btn-sm btn-info" onclick="simulateSessionExpiry()">
                                    <i class="fas fa-clock"></i>
                                    Simulate Expiry
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="testSessionValidation()">
                                    <i class="fas fa-check-circle"></i>
                                    Test Validation
                                </button>
                                <button class="btn btn-sm btn-outline" onclick="generateSessionReport()">
                                    <i class="fas fa-file-alt"></i>
                                    Generate Report
                                </button>
                            </div>
                        </div>

                        <div id="controlResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../../assets/js/utils/api.js"></script>
    <script src="../../assets/js/utils/auth.js"></script>
    <script src="../../assets/js/utils/storage.js"></script>
    <script src="../../assets/js/utils/notifications.js"></script>
    <script src="../../assets/js/shared/components.js"></script>
    <script src="../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables
        let autoRefreshInterval = null;
        let currentSessions = [];
        let currentSessionData = null;
        let trustedDevices = [];
        let sessionActivity = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupTabs();
            setupForms();
            startAutoRefresh();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load initial data if authenticated
            if (window.authManager.isAuthenticated()) {
                await loadCurrentSession();
                await loadAllSessions();
            }
        }

        function setupTabs() {
            const tabs = document.querySelectorAll('.session-tab');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    tab.classList.add('active');

                    // Show corresponding content
                    const tabId = tab.dataset.tab + '-tab';
                    const content = document.getElementById(tabId);
                    if (content) {
                        content.classList.add('active');
                    }
                });
            });
        }

        function setupForms() {
            // Terminate session form
            const terminateForm = document.getElementById('terminateSessionForm');
            terminateForm.addEventListener('submit', handleTerminateSession);
        }

        // Auto-refresh functionality
        function startAutoRefresh() {
            autoRefreshInterval = setInterval(async () => {
                if (window.authManager.isAuthenticated()) {
                    await loadCurrentSession();
                    // Only refresh sessions if on sessions tab
                    const activeTab = document.querySelector('.session-tab.active');
                    if (activeTab && activeTab.dataset.tab === 'sessions') {
                        await loadAllSessions();
                    }
                }
            }, 30000); // 30 seconds
        }

        function toggleAutoRefresh() {
            const button = document.getElementById('autoRefreshText');
            const indicator = document.querySelector('.auto-refresh');

            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                button.textContent = 'Enable';
                indicator.classList.remove('auto-refresh');
            } else {
                startAutoRefresh();
                button.textContent = 'Disable';
                indicator.classList.add('auto-refresh');
            }
        }

        // Session Management Functions
        async function loadCurrentSession() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.sessions.current();

                if (response.success) {
                    currentSessionData = response.data.session;
                    displayCurrentSessionInfo(response.data.session);
                    displayCurrentSessionDetails(response.data.session);
                    showResponse('currentResponse', response, 'success');
                } else {
                    showResponse('currentResponse', response, 'error');
                }
            } catch (error) {
                showResponse('currentResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        async function loadAllSessions() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.sessions.list(50, 0);

                if (response.success) {
                    currentSessions = response.data.sessions;
                    displaySessionsTable(response.data.sessions);
                    showResponse('sessionsResponse', response, 'success');
                } else {
                    showResponse('sessionsResponse', response, 'error');
                }
            } catch (error) {
                showResponse('sessionsResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        async function loadTrustedDevices() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.sessions.getTrustedDevices();

                if (response.success) {
                    trustedDevices = response.data.devices;
                    displayTrustedDevices(response.data.devices);
                    showResponse('devicesResponse', response, 'success');
                } else {
                    showResponse('devicesResponse', response, 'error');
                }
            } catch (error) {
                showResponse('devicesResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        async function loadSessionActivity(limit = 20) {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.sessions.getActivity(limit, 0);

                if (response.success) {
                    sessionActivity = response.data.activities;
                    displayActivityTimeline(response.data.activities);
                    showResponse('activityResponse', response, 'success');
                } else {
                    showResponse('activityResponse', response, 'error');
                }
            } catch (error) {
                showResponse('activityResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }