<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Factor Authentication Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        .mfa-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .mfa-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
            white-space: nowrap;
        }

        .mfa-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .mfa-tab:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .mfa-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .qr-code-container {
            text-align: center;
            padding: 2rem;
            background-color: var(--bg-secondary);
            border-radius: 0.75rem;
            margin: 1rem 0;
        }

        .qr-code {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .setup-secret {
            font-family: monospace;
            background-color: var(--bg-tertiary);
            padding: 0.75rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
            word-break: break-all;
            margin: 1rem 0;
        }

        .backup-codes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .backup-code {
            font-family: monospace;
            background-color: var(--bg-tertiary);
            padding: 0.5rem;
            border-radius: 0.25rem;
            text-align: center;
            border: 1px solid var(--border-color);
            font-size: 0.875rem;
        }

        .totp-input {
            text-align: center;
            font-family: monospace;
            font-size: 1.5rem;
            letter-spacing: 0.5rem;
            padding: 1rem;
        }

        .mfa-status {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .mfa-status.enabled {
            background-color: rgba(16, 185, 129, 0.1);
            border: 1px solid var(--success-color);
            color: var(--success-color);
        }

        .mfa-status.disabled {
            background-color: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
        }

        .response-viewer {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .field-error {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .form-input.error {
            border-color: var(--error-color);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: inherit;
        }

        [data-theme="dark"] .loading-overlay {
            background-color: rgba(15, 23, 42, 0.8);
        }

        .quick-test-buttons {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .test-result.success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
        }

        .test-result.error {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--error-color);
        }

        .step-indicator {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
        }

        .step.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .step.completed {
            background-color: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        @media (max-width: 768px) {
            .mfa-tabs {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            .step-indicator {
                flex-direction: column;
                align-items: stretch;
            }

            .step {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../../index.html" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-arrow-left"></i>
                        <i class="fas fa-mobile-alt"></i>
                        <div>
                            <h1>Multi-Factor Authentication</h1>
                            <span class="subtitle">TOTP Setup, Verification & Management</span>
                        </div>
                    </a>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- MFA Status Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3>MFA Status Overview</h3>
                    <p>Current multi-factor authentication configuration</p>
                </div>
                <div class="card-body">
                    <div id="mfaStatusDisplay" class="mfa-status disabled">
                        <i class="fas fa-shield-alt"></i>
                        <div>
                            <strong>MFA Status: Disabled</strong>
                            <p>Multi-factor authentication is not enabled for this account.</p>
                        </div>
                    </div>
                    <div class="quick-test-buttons">
                        <button class="btn btn-sm btn-primary" onclick="checkMFAStatus()">
                            <i class="fas fa-sync"></i>
                            Check Status
                        </button>
                        <button class="btn btn-sm btn-info" onclick="loadMFADemo()">
                            <i class="fas fa-play"></i>
                            Demo Mode
                        </button>
                    </div>
                </div>
            </div>

            <!-- MFA Tabs -->
            <div class="mfa-tabs">
                <button class="mfa-tab active" data-tab="setup">
                    <i class="fas fa-cog"></i>
                    MFA Setup
                </button>
                <button class="mfa-tab" data-tab="verify">
                    <i class="fas fa-key"></i>
                    TOTP Verification
                </button>
                <button class="mfa-tab" data-tab="backup">
                    <i class="fas fa-shield-alt"></i>
                    Backup Codes
                </button>
                <button class="mfa-tab" data-tab="management">
                    <i class="fas fa-tools"></i>
                    MFA Management
                </button>
                <button class="mfa-tab" data-tab="login">
                    <i class="fas fa-sign-in-alt"></i>
                    MFA Login Flow
                </button>
            </div>

            <!-- MFA Setup Tab -->
            <div class="tab-content active" id="setup-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>MFA Setup Process</h3>
                        <p>Initialize and complete TOTP multi-factor authentication setup</p>
                    </div>
                    <div class="card-body">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step active" id="step1">
                                <i class="fas fa-play"></i>
                                <span>1. Initialize</span>
                            </div>
                            <div class="step" id="step2">
                                <i class="fas fa-qrcode"></i>
                                <span>2. Scan QR</span>
                            </div>
                            <div class="step" id="step3">
                                <i class="fas fa-check"></i>
                                <span>3. Verify</span>
                            </div>
                        </div>

                        <!-- Step 1: Initialize Setup -->
                        <div id="setupStep1" class="setup-step">
                            <h4>Step 1: Initialize MFA Setup</h4>
                            <p>Start the MFA setup process to generate a QR code and secret key.</p>

                            <div class="quick-test-buttons">
                                <button class="btn btn-primary" onclick="initializeMFASetup()">
                                    <i class="fas fa-play"></i>
                                    Initialize MFA Setup
                                </button>
                                <button class="btn btn-secondary" onclick="clearSetupForm()">
                                    <i class="fas fa-eraser"></i>
                                    Clear
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: QR Code Display -->
                        <div id="setupStep2" class="setup-step" style="display: none;">
                            <h4>Step 2: Scan QR Code</h4>
                            <p>Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)</p>

                            <div class="qr-code-container">
                                <div class="qr-code">
                                    <canvas id="qrCodeCanvas"></canvas>
                                </div>
                                <p><strong>Or enter this secret manually:</strong></p>
                                <div class="setup-secret" id="setupSecret">
                                    <!-- Secret will be displayed here -->
                                </div>
                                <button class="btn btn-sm btn-outline" onclick="copySecret()">
                                    <i class="fas fa-copy"></i>
                                    Copy Secret
                                </button>
                            </div>

                            <div class="text-center">
                                <button class="btn btn-primary" onclick="proceedToVerification()">
                                    <i class="fas fa-arrow-right"></i>
                                    I've Added the Account
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Verification -->
                        <div id="setupStep3" class="setup-step" style="display: none;">
                            <h4>Step 3: Verify TOTP Code</h4>
                            <p>Enter the 6-digit code from your authenticator app to complete setup.</p>

                            <form id="setupVerificationForm" class="mfa-form">
                                <div class="form-group">
                                    <label class="form-label" for="setupTotpCode">TOTP Code</label>
                                    <input type="text" id="setupTotpCode" name="totpCode" class="form-input totp-input"
                                           placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                                    <small class="text-muted">Enter the 6-digit code from your authenticator app</small>
                                </div>

                                <button type="submit" class="btn btn-success" style="width: 100%;">
                                    <i class="fas fa-check"></i>
                                    Complete MFA Setup
                                </button>
                            </form>
                        </div>

                        <div id="setupResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- TOTP Verification Tab -->
            <div class="tab-content" id="verify-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>TOTP Code Verification</h3>
                        <p>Test TOTP code verification (requires MFA to be enabled)</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-info" onclick="generateTestCode()">
                                <i class="fas fa-random"></i>
                                Generate Test Code
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="clearVerifyForm()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <form id="verifyTotpForm" class="mfa-form">
                            <div class="form-group">
                                <label class="form-label" for="verifyTotpCode">TOTP Code</label>
                                <input type="text" id="verifyTotpCode" name="totpCode" class="form-input totp-input"
                                       placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                                <small class="text-muted">Enter the current 6-digit code from your authenticator app</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="mfaToken">MFA Token (Optional)</label>
                                <input type="text" id="mfaToken" name="mfaToken" class="form-input"
                                       placeholder="Enter MFA token from login attempt">
                                <small class="text-muted">Required only when verifying during login flow</small>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <i class="fas fa-check"></i>
                                Verify TOTP Code
                            </button>
                        </form>

                        <div id="verifyResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Backup Codes Tab -->
            <div class="tab-content" id="backup-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Backup Codes Management</h3>
                        <p>Generate, view, and test MFA backup codes</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Important:</strong> Backup codes are single-use only. Store them securely and use them only when you cannot access your authenticator app.
                        </div>

                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="generateBackupCodes()">
                                <i class="fas fa-plus"></i>
                                Generate New Codes
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="downloadBackupCodes()">
                                <i class="fas fa-download"></i>
                                Download Codes
                            </button>
                            <button class="btn btn-sm btn-info" onclick="clearBackupForm()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <!-- Backup Codes Display -->
                        <div id="backupCodesDisplay" style="display: none;">
                            <h4>Your Backup Codes</h4>
                            <p>Save these codes in a secure location. Each code can only be used once.</p>
                            <div class="backup-codes" id="backupCodesList">
                                <!-- Backup codes will be displayed here -->
                            </div>
                            <div class="text-center mt-3">
                                <button class="btn btn-outline" onclick="copyAllBackupCodes()">
                                    <i class="fas fa-copy"></i>
                                    Copy All Codes
                                </button>
                            </div>
                        </div>

                        <!-- Test Backup Code -->
                        <div class="mt-4">
                            <h4>Test Backup Code</h4>
                            <form id="testBackupCodeForm" class="mfa-form">
                                <div class="form-group">
                                    <label class="form-label" for="backupCodeInput">Backup Code</label>
                                    <input type="text" id="backupCodeInput" name="backupCode" class="form-input"
                                           placeholder="Enter backup code" required>
                                    <small class="text-muted">Enter one of your backup codes to test verification</small>
                                </div>

                                <button type="submit" class="btn btn-warning" style="width: 100%;">
                                    <i class="fas fa-key"></i>
                                    Verify Backup Code
                                </button>
                            </form>
                        </div>

                        <div id="backupResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- MFA Management Tab -->
            <div class="tab-content" id="management-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>MFA Management</h3>
                        <p>Enable, disable, and manage MFA settings</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="checkMFAStatus()">
                                <i class="fas fa-sync"></i>
                                Refresh Status
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="showDisableMFA()">
                                <i class="fas fa-times"></i>
                                Disable MFA
                            </button>
                        </div>

                        <!-- Current MFA Status -->
                        <div class="mb-4">
                            <h4>Current Status</h4>
                            <div id="managementStatusDisplay" class="mfa-status disabled">
                                <i class="fas fa-shield-alt"></i>
                                <div>
                                    <strong>Loading...</strong>
                                    <p>Checking MFA status...</p>
                                </div>
                            </div>
                        </div>

                        <!-- Disable MFA Form -->
                        <div id="disableMFASection" style="display: none;">
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Warning:</strong> Disabling MFA will reduce your account security. You will need to enter your TOTP code to confirm.
                            </div>

                            <form id="disableMFAForm" class="mfa-form">
                                <div class="form-group">
                                    <label class="form-label" for="disableTotpCode">TOTP Code</label>
                                    <input type="text" id="disableTotpCode" name="totpCode" class="form-input totp-input"
                                           placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                                    <small class="text-muted">Enter your current TOTP code to disable MFA</small>
                                </div>

                                <div class="form-group">
                                    <div class="form-checkbox">
                                        <input type="checkbox" id="confirmDisable" name="confirmDisable" required>
                                        <label for="confirmDisable">I understand that disabling MFA reduces my account security</label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-error" style="width: 100%;">
                                    <i class="fas fa-times"></i>
                                    Disable MFA
                                </button>
                            </form>
                        </div>

                        <!-- Regenerate Backup Codes -->
                        <div class="mt-4">
                            <h4>Regenerate Backup Codes</h4>
                            <p>Generate new backup codes (this will invalidate all existing backup codes)</p>

                            <form id="regenerateCodesForm" class="mfa-form">
                                <div class="form-group">
                                    <label class="form-label" for="regenerateTotpCode">TOTP Code</label>
                                    <input type="text" id="regenerateTotpCode" name="totpCode" class="form-input totp-input"
                                           placeholder="000000" maxlength="6" pattern="[0-9]{6}" required>
                                    <small class="text-muted">Enter your current TOTP code to regenerate backup codes</small>
                                </div>

                                <button type="submit" class="btn btn-warning" style="width: 100%;">
                                    <i class="fas fa-sync"></i>
                                    Regenerate Backup Codes
                                </button>
                            </form>
                        </div>

                        <div id="managementResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- MFA Login Flow Tab -->
            <div class="tab-content" id="login-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>MFA Login Flow Testing</h3>
                        <p>Test the complete MFA-enabled login process</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Info:</strong> This simulates the complete login flow when MFA is enabled. First login with credentials, then verify with MFA.
                        </div>

                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="fillTestMFAUser()">
                                <i class="fas fa-user"></i>
                                Test MFA User
                            </button>
                            <button class="btn btn-sm btn-info" onclick="clearLoginFlowForm()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <!-- Step 1: Initial Login -->
                        <div id="loginStep1">
                            <h4>Step 1: Login with Credentials</h4>
                            <form id="mfaLoginForm" class="mfa-form">
                                <div class="form-group">
                                    <label class="form-label" for="mfaLoginEmail">Email</label>
                                    <input type="email" id="mfaLoginEmail" name="email" class="form-input"
                                           placeholder="Enter email address" required>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="mfaLoginPassword">Password</label>
                                    <input type="password" id="mfaLoginPassword" name="password" class="form-input"
                                           placeholder="Enter password" required>
                                </div>

                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    <i class="fas fa-sign-in-alt"></i>
                                    Login
                                </button>
                            </form>
                        </div>

                        <!-- Step 2: MFA Verification -->
                        <div id="loginStep2" style="display: none;">
                            <h4>Step 2: MFA Verification</h4>
                            <div class="alert alert-warning">
                                <i class="fas fa-mobile-alt"></i>
                                <strong>MFA Required:</strong> Please enter your TOTP code or backup code to complete login.
                            </div>

                            <form id="mfaVerificationForm" class="mfa-form">
                                <div class="form-group">
                                    <label class="form-label" for="loginMfaCode">TOTP Code</label>
                                    <input type="text" id="loginMfaCode" name="totpCode" class="form-input totp-input"
                                           placeholder="000000" maxlength="6" pattern="[0-9]{6}">
                                    <small class="text-muted">Enter the 6-digit code from your authenticator app</small>
                                </div>

                                <div class="text-center mb-3">
                                    <span class="text-muted">OR</span>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="loginBackupCode">Backup Code</label>
                                    <input type="text" id="loginBackupCode" name="backupCode" class="form-input"
                                           placeholder="Enter backup code">
                                    <small class="text-muted">Use a backup code if you can't access your authenticator</small>
                                </div>

                                <button type="submit" class="btn btn-success" style="width: 100%;">
                                    <i class="fas fa-check"></i>
                                    Complete Login
                                </button>
                            </form>
                        </div>

                        <div id="loginFlowResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../../assets/js/utils/api.js"></script>
    <script src="../../assets/js/utils/auth.js"></script>
    <script src="../../assets/js/utils/storage.js"></script>
    <script src="../../assets/js/utils/notifications.js"></script>
    <script src="../../assets/js/shared/components.js"></script>
    <script src="../../assets/js/shared/constants.js"></script>
    <script>
        // Global variables for MFA setup
        let currentSetupToken = null;
        let currentMfaToken = null;
        let currentSecret = null;
        let currentBackupCodes = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupTabs();
            setupForms();
            setupTOTPInputs();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Check MFA status if authenticated
            if (window.authManager.isAuthenticated()) {
                await checkMFAStatus();
            }
        }

        function setupTabs() {
            const tabs = document.querySelectorAll('.mfa-tab');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    tab.classList.add('active');

                    // Show corresponding content
                    const tabId = tab.dataset.tab + '-tab';
                    const content = document.getElementById(tabId);
                    if (content) {
                        content.classList.add('active');
                    }
                });
            });
        }

        function setupForms() {
            // Setup verification form
            const setupForm = document.getElementById('setupVerificationForm');
            setupForm.addEventListener('submit', handleSetupVerification);

            // TOTP verification form
            const verifyForm = document.getElementById('verifyTotpForm');
            verifyForm.addEventListener('submit', handleTOTPVerification);

            // Backup code test form
            const backupForm = document.getElementById('testBackupCodeForm');
            backupForm.addEventListener('submit', handleBackupCodeTest);

            // Disable MFA form
            const disableForm = document.getElementById('disableMFAForm');
            disableForm.addEventListener('submit', handleDisableMFA);

            // Regenerate codes form
            const regenerateForm = document.getElementById('regenerateCodesForm');
            regenerateForm.addEventListener('submit', handleRegenerateBackupCodes);

            // MFA login form
            const loginForm = document.getElementById('mfaLoginForm');
            loginForm.addEventListener('submit', handleMFALogin);

            // MFA verification form
            const mfaVerifyForm = document.getElementById('mfaVerificationForm');
            mfaVerifyForm.addEventListener('submit', handleMFALoginVerification);
        }

        function setupTOTPInputs() {
            // Auto-format TOTP inputs
            const totpInputs = document.querySelectorAll('.totp-input');
            totpInputs.forEach(input => {
                input.addEventListener('input', (e) => {
                    // Only allow numbers
                    e.target.value = e.target.value.replace(/[^0-9]/g, '');

                    // Auto-submit when 6 digits are entered (optional)
                    if (e.target.value.length === 6) {
                        // Could auto-submit form here if desired
                    }
                });

                input.addEventListener('paste', (e) => {
                    e.preventDefault();
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    const numbers = paste.replace(/[^0-9]/g, '').substring(0, 6);
                    e.target.value = numbers;
                });
            });
        }

        // MFA Setup Functions
        async function initializeMFASetup() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                showLoading(document.getElementById('setupStep1'));
                clearResponse('setupResponse');

                const response = await window.apiClient.mfa.initializeSetup();

                if (response.success) {
                    currentSetupToken = response.data.setupToken;
                    currentSecret = response.data.secret;

                    // Generate QR code
                    await generateQRCode(response.data.qrCodeUrl);

                    // Display secret
                    document.getElementById('setupSecret').textContent = response.data.secret;

                    // Show step 2
                    showSetupStep(2);

                    showResponse('setupResponse', response, 'success');
                } else {
                    showResponse('setupResponse', response, 'error');
                }
            } catch (error) {
                showResponse('setupResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(document.getElementById('setupStep1'));
            }
        }

        async function generateQRCode(qrCodeUrl) {
            try {
                const canvas = document.getElementById('qrCodeCanvas');
                await QRCode.toCanvas(canvas, qrCodeUrl, {
                    width: 200,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                });
            } catch (error) {
                console.error('Failed to generate QR code:', error);
                window.notificationManager.error('Failed to generate QR code');
            }
        }

        function showSetupStep(step) {
            // Hide all steps
            document.querySelectorAll('.setup-step').forEach(el => {
                el.style.display = 'none';
            });

            // Show current step
            document.getElementById(`setupStep${step}`).style.display = 'block';

            // Update step indicators
            for (let i = 1; i <= 3; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed');

                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }

        function proceedToVerification() {
            showSetupStep(3);
        }

        async function handleSetupVerification(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            if (!currentSetupToken) {
                window.notificationManager.error('No setup token available. Please restart setup.');
                return;
            }

            showLoading(form);
            clearResponse('setupResponse');

            try {
                const response = await window.apiClient.mfa.completeSetup(
                    currentSetupToken,
                    data.totpCode
                );

                if (response.success) {
                    showResponse('setupResponse', response, 'success');

                    // Store backup codes if provided
                    if (response.data.backupCodes) {
                        currentBackupCodes = response.data.backupCodes;
                        displayBackupCodes(response.data.backupCodes);
                    }

                    // Update MFA status
                    await checkMFAStatus();

                    // Reset setup
                    currentSetupToken = null;
                    currentSecret = null;

                    window.notificationManager.success('MFA setup completed successfully!');
                } else {
                    showResponse('setupResponse', response, 'error');
                }
            } catch (error) {
                showResponse('setupResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        // TOTP Verification Functions
        async function handleTOTPVerification(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('verifyResponse');

            try {
                const response = await window.apiClient.mfa.verifyTotp(
                    data.totpCode,
                    data.mfaToken || null
                );

                showResponse('verifyResponse', response, response.success ? 'success' : 'error');

                if (response.success) {
                    window.notificationManager.success('TOTP code verified successfully!');
                }
            } catch (error) {
                showResponse('verifyResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        // Backup Codes Functions
        async function generateBackupCodes() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                clearResponse('backupResponse');

                const response = await window.apiClient.mfa.regenerateBackupCodes('123456'); // Demo code

                if (response.success && response.data.backupCodes) {
                    currentBackupCodes = response.data.backupCodes;
                    displayBackupCodes(response.data.backupCodes);
                    showResponse('backupResponse', response, 'success');
                } else {
                    showResponse('backupResponse', response, 'error');
                }
            } catch (error) {
                showResponse('backupResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        function displayBackupCodes(codes) {
            const container = document.getElementById('backupCodesDisplay');
            const codesList = document.getElementById('backupCodesList');

            codesList.innerHTML = '';
            codes.forEach(code => {
                const codeElement = document.createElement('div');
                codeElement.className = 'backup-code';
                codeElement.textContent = code;
                codesList.appendChild(codeElement);
            });

            container.style.display = 'block';
        }

        async function handleBackupCodeTest(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('backupResponse');

            try {
                const response = await window.apiClient.mfa.verifyBackupCode(data.backupCode);

                showResponse('backupResponse', response, response.success ? 'success' : 'error');

                if (response.success) {
                    window.notificationManager.success('Backup code verified successfully!');
                    form.reset();
                }
            } catch (error) {
                showResponse('backupResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        // MFA Management Functions
        async function checkMFAStatus() {
            if (!window.authManager.isAuthenticated()) {
                updateMFAStatusDisplay(false, 'Please log in to check MFA status');
                return;
            }

            try {
                const response = await window.apiClient.mfa.getStatus();

                if (response.success) {
                    const isEnabled = response.data.mfaEnabled;
                    updateMFAStatusDisplay(isEnabled, response.data);

                    // Update management tab status
                    updateManagementStatusDisplay(isEnabled, response.data);
                } else {
                    updateMFAStatusDisplay(false, 'Failed to check MFA status');
                }
            } catch (error) {
                updateMFAStatusDisplay(false, 'Error checking MFA status: ' + error.message);
            }
        }

        function updateMFAStatusDisplay(isEnabled, data) {
            const statusElement = document.getElementById('mfaStatusDisplay');
            const icon = statusElement.querySelector('i');
            const strong = statusElement.querySelector('strong');
            const p = statusElement.querySelector('p');

            if (isEnabled) {
                statusElement.className = 'mfa-status enabled';
                icon.className = 'fas fa-shield-check';
                strong.textContent = 'MFA Status: Enabled';
                p.textContent = typeof data === 'object' ?
                    `MFA is active. Setup date: ${new Date(data.setupDate || Date.now()).toLocaleDateString()}` :
                    'Multi-factor authentication is enabled for this account.';
            } else {
                statusElement.className = 'mfa-status disabled';
                icon.className = 'fas fa-shield-alt';
                strong.textContent = 'MFA Status: Disabled';
                p.textContent = typeof data === 'string' ? data : 'Multi-factor authentication is not enabled for this account.';
            }
        }

        function updateManagementStatusDisplay(isEnabled, data) {
            const statusElement = document.getElementById('managementStatusDisplay');
            const icon = statusElement.querySelector('i');
            const strong = statusElement.querySelector('strong');
            const p = statusElement.querySelector('p');

            if (isEnabled) {
                statusElement.className = 'mfa-status enabled';
                icon.className = 'fas fa-shield-check';
                strong.textContent = 'MFA Enabled';
                p.textContent = `Setup on: ${new Date(data.setupDate || Date.now()).toLocaleDateString()}`;
            } else {
                statusElement.className = 'mfa-status disabled';
                icon.className = 'fas fa-shield-alt';
                strong.textContent = 'MFA Disabled';
                p.textContent = 'Multi-factor authentication is not enabled.';
            }
        }

        function showDisableMFA() {
            const section = document.getElementById('disableMFASection');
            section.style.display = section.style.display === 'none' ? 'block' : 'none';
        }

        async function handleDisableMFA(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            if (!data.confirmDisable) {
                window.notificationManager.error('Please confirm that you want to disable MFA');
                return;
            }

            showLoading(form);
            clearResponse('managementResponse');

            try {
                const response = await window.apiClient.mfa.disable(data.totpCode);

                showResponse('managementResponse', response, response.success ? 'success' : 'error');

                if (response.success) {
                    window.notificationManager.success('MFA disabled successfully');
                    await checkMFAStatus();
                    document.getElementById('disableMFASection').style.display = 'none';
                    form.reset();
                }
            } catch (error) {
                showResponse('managementResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleRegenerateBackupCodes(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('managementResponse');

            try {
                const response = await window.apiClient.mfa.regenerateBackupCodes(data.totpCode);

                if (response.success && response.data.backupCodes) {
                    currentBackupCodes = response.data.backupCodes;
                    displayBackupCodes(response.data.backupCodes);
                    showResponse('managementResponse', response, 'success');
                    window.notificationManager.success('Backup codes regenerated successfully');
                    form.reset();
                } else {
                    showResponse('managementResponse', response, 'error');
                }
            } catch (error) {
                showResponse('managementResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        // MFA Login Flow Functions
        async function handleMFALogin(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('loginFlowResponse');

            try {
                const response = await window.authManager.login({
                    email: data.email,
                    password: data.password
                });

                if (response.requiresMFA) {
                    currentMfaToken = response.mfaToken;
                    showLoginStep(2);
                    showResponse('loginFlowResponse', {
                        success: true,
                        message: 'MFA required. Please enter your TOTP code or backup code.',
                        data: { mfaToken: response.mfaToken }
                    }, 'success');
                } else if (response.success) {
                    showResponse('loginFlowResponse', {
                        success: true,
                        message: 'Login successful! (No MFA required)',
                        data: response
                    }, 'success');
                } else {
                    showResponse('loginFlowResponse', response, 'error');
                }
            } catch (error) {
                showResponse('loginFlowResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleMFALoginVerification(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            if (!currentMfaToken) {
                window.notificationManager.error('No MFA token available. Please restart login.');
                return;
            }

            showLoading(form);
            clearResponse('loginFlowResponse');

            try {
                let response;

                if (data.totpCode) {
                    // Verify with TOTP code
                    response = await window.authManager.verifyMFA(data.totpCode);
                } else if (data.backupCode) {
                    // Verify with backup code
                    response = await window.apiClient.mfa.verifyBackupCode(data.backupCode);
                } else {
                    throw new Error('Please enter either a TOTP code or backup code');
                }

                if (response.success) {
                    showResponse('loginFlowResponse', {
                        success: true,
                        message: 'MFA verification successful! Login completed.',
                        data: response
                    }, 'success');

                    updateAuthStatus();
                    currentMfaToken = null;

                    window.notificationManager.success('Login completed successfully!');
                } else {
                    showResponse('loginFlowResponse', response, 'error');
                }
            } catch (error) {
                showResponse('loginFlowResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        function showLoginStep(step) {
            document.getElementById('loginStep1').style.display = step === 1 ? 'block' : 'none';
            document.getElementById('loginStep2').style.display = step === 2 ? 'block' : 'none';
        }

        // Utility Functions
        function copySecret() {
            if (currentSecret) {
                Utils.copyToClipboard(currentSecret);
            } else {
                window.notificationManager.error('No secret available to copy');
            }
        }

        function copyAllBackupCodes() {
            if (currentBackupCodes.length > 0) {
                const codesText = currentBackupCodes.join('\n');
                Utils.copyToClipboard(codesText);
            } else {
                window.notificationManager.error('No backup codes available to copy');
            }
        }

        function downloadBackupCodes() {
            if (currentBackupCodes.length === 0) {
                window.notificationManager.error('No backup codes available to download');
                return;
            }

            const codesText = `Backup Codes for MFA\nGenerated: ${new Date().toLocaleString()}\n\n${currentBackupCodes.join('\n')}\n\nIMPORTANT: Store these codes securely. Each code can only be used once.`;
            Utils.downloadFile(codesText, 'mfa-backup-codes.txt', 'text/plain');
        }

        function generateTestCode() {
            // Generate a random 6-digit code for testing
            const testCode = Math.floor(100000 + Math.random() * 900000).toString();
            document.getElementById('verifyTotpCode').value = testCode;
            window.notificationManager.info(`Generated test code: ${testCode}`);
        }

        function fillTestMFAUser() {
            document.getElementById('mfaLoginEmail').value = '<EMAIL>';
            document.getElementById('mfaLoginPassword').value = 'MFAPassword123!';
        }

        function loadMFADemo() {
            // Load demo data for testing
            currentBackupCodes = [
                'ABC123DEF456',
                'GHI789JKL012',
                'MNO345PQR678',
                'STU901VWX234',
                'YZA567BCD890',
                'EFG123HIJ456',
                'KLM789NOP012',
                'QRS345TUV678'
            ];

            displayBackupCodes(currentBackupCodes);
            updateMFAStatusDisplay(true, { setupDate: new Date().toISOString() });
            updateManagementStatusDisplay(true, { setupDate: new Date().toISOString() });

            window.notificationManager.info('Demo mode loaded with sample data');
        }

        // Clear form functions
        function clearSetupForm() {
            document.querySelectorAll('#setup-tab form').forEach(form => form.reset());
            clearResponse('setupResponse');
            showSetupStep(1);
            currentSetupToken = null;
            currentSecret = null;
        }

        function clearVerifyForm() {
            document.getElementById('verifyTotpForm').reset();
            clearResponse('verifyResponse');
        }

        function clearBackupForm() {
            document.getElementById('testBackupCodeForm').reset();
            clearResponse('backupResponse');
            document.getElementById('backupCodesDisplay').style.display = 'none';
        }

        function clearLoginFlowForm() {
            document.getElementById('mfaLoginForm').reset();
            document.getElementById('mfaVerificationForm').reset();
            clearResponse('loginFlowResponse');
            showLoginStep(1);
            currentMfaToken = null;
        }

        // UI utility functions
        function showLoading(element) {
            const button = element.querySelector('button[type="submit"]') || element;
            if (button.tagName === 'BUTTON') {
                button.disabled = true;
                const originalText = button.innerHTML;
                button.setAttribute('data-original-text', originalText);
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            }
        }

        function hideLoading(element) {
            const button = element.querySelector('button[type="submit"]') || element;
            if (button.tagName === 'BUTTON') {
                button.disabled = false;
                const originalText = button.getAttribute('data-original-text');
                if (originalText) {
                    button.innerHTML = originalText;
                    button.removeAttribute('data-original-text');
                }
            }
        }

        function showResponse(elementId, response, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'block';
                element.innerHTML = Utils.highlightJSON(response);
                element.className = `response-viewer ${type}`;
            }
        }

        function clearResponse(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
                element.innerHTML = '';
            }
        }

        // Shared utility functions from main page
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();

                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    </script>
</body>
</html>