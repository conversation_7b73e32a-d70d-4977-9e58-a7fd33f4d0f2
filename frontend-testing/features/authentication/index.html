<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Testing - Secure Backend API</title>
    <link rel="stylesheet" href="../../assets/css/main.css">
    <link rel="stylesheet" href="../../assets/css/components.css">
    <link rel="stylesheet" href="../../assets/css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .auth-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 2rem;
        }

        .auth-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            transition: all 0.2s ease;
            border-bottom: 2px solid transparent;
        }

        .auth-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .auth-tab:hover {
            color: var(--text-primary);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .auth-form {
            max-width: 500px;
            margin: 0 auto;
        }

        .response-viewer {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .field-error {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .form-input.error {
            border-color: var(--error-color);
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: inherit;
        }

        [data-theme="dark"] .loading-overlay {
            background-color: rgba(15, 23, 42, 0.8);
        }

        .quick-test-buttons {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .test-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border-color);
        }

        .test-result.success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--success-color);
        }

        .test-result.error {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--error-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../../index.html" style="text-decoration: none; color: inherit; display: flex; align-items: center; gap: 0.75rem;">
                        <i class="fas fa-arrow-left"></i>
                        <i class="fas fa-key"></i>
                        <div>
                            <h1>Authentication Testing</h1>
                            <span class="subtitle">User Registration, Login & Profile Management</span>
                        </div>
                    </a>
                </div>
                <div class="header-actions">
                    <div class="server-status" id="serverStatus">
                        <i class="fas fa-circle status-indicator"></i>
                        <span class="status-text">Checking...</span>
                    </div>
                    <div class="auth-status" id="authStatus">
                        <i class="fas fa-user-circle"></i>
                        <span class="auth-text">Not Authenticated</span>
                    </div>
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Authentication Tabs -->
            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="login">
                    <i class="fas fa-sign-in-alt"></i>
                    Login
                </button>
                <button class="auth-tab" data-tab="register">
                    <i class="fas fa-user-plus"></i>
                    Register
                </button>
                <button class="auth-tab" data-tab="profile">
                    <i class="fas fa-user-edit"></i>
                    Profile
                </button>
                <button class="auth-tab" data-tab="password">
                    <i class="fas fa-key"></i>
                    Password
                </button>
                <button class="auth-tab" data-tab="forgot">
                    <i class="fas fa-unlock-alt"></i>
                    Forgot Password
                </button>
                <button class="auth-tab" data-tab="verify">
                    <i class="fas fa-envelope-circle-check"></i>
                    Email Verification
                </button>
                <button class="auth-tab" data-tab="permissions">
                    <i class="fas fa-shield-alt"></i>
                    Permissions
                </button>
            </div>

            <!-- Login Tab -->
            <div class="tab-content active" id="login-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>User Login</h3>
                        <p>Test user authentication with email/username and password</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="fillTestUser()">
                                <i class="fas fa-user"></i>
                                Test User
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="fillAdminUser()">
                                <i class="fas fa-user-shield"></i>
                                Admin User
                            </button>
                            <button class="btn btn-sm btn-info" onclick="clearLoginForm()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <form id="loginForm" class="auth-form">
                            <div class="form-group">
                                <label class="form-label" for="loginEmail">Email or Username</label>
                                <input type="text" id="loginEmail" name="email" class="form-input" placeholder="Enter email or username" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="loginPassword">Password</label>
                                <input type="password" id="loginPassword" name="password" class="form-input" placeholder="Enter password" required>
                            </div>

                            <div class="form-group">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="rememberMe" name="rememberMe">
                                    <label for="rememberMe">Remember me</label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <i class="fas fa-sign-in-alt"></i>
                                Login
                            </button>
                        </form>

                        <div id="loginResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Register Tab -->
            <div class="tab-content" id="register-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>User Registration</h3>
                        <p>Test new user account creation with validation</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-secondary" onclick="fillTestRegistration()">
                                <i class="fas fa-magic"></i>
                                Generate Test Data
                            </button>
                            <button class="btn btn-sm btn-info" onclick="clearRegisterForm()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <form id="registerForm" class="auth-form">
                            <div class="form-group">
                                <label class="form-label" for="registerEmail">Email Address</label>
                                <input type="email" id="registerEmail" name="email" class="form-input" placeholder="Enter email address" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="registerUsername">Username</label>
                                <input type="text" id="registerUsername" name="username" class="form-input" placeholder="Enter username" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="registerFirstName">First Name</label>
                                <input type="text" id="registerFirstName" name="firstName" class="form-input" placeholder="Enter first name" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="registerLastName">Last Name</label>
                                <input type="text" id="registerLastName" name="lastName" class="form-input" placeholder="Enter last name" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="registerPassword">Password</label>
                                <input type="password" id="registerPassword" name="password" class="form-input" placeholder="Enter password" required>
                                <div class="password-strength" id="passwordStrength" style="margin-top: 0.5rem; display: none;">
                                    <div class="strength-bar" style="height: 4px; background: var(--bg-tertiary); border-radius: 2px;">
                                        <div class="strength-fill" style="height: 100%; border-radius: 2px; transition: all 0.3s ease; width: 0%;"></div>
                                    </div>
                                    <div class="strength-text" style="font-size: 0.75rem; margin-top: 0.25rem;"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="registerConfirmPassword">Confirm Password</label>
                                <input type="password" id="registerConfirmPassword" name="confirmPassword" class="form-input" placeholder="Confirm password" required>
                            </div>

                            <div class="form-group">
                                <div class="form-checkbox">
                                    <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                                    <label for="agreeTerms">I agree to the Terms of Service and Privacy Policy</label>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <i class="fas fa-user-plus"></i>
                                Register
                            </button>
                        </form>

                        <div id="registerResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Profile Tab -->
            <div class="tab-content" id="profile-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Profile Management</h3>
                        <p>View and update user profile information</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadProfile()">
                                <i class="fas fa-sync"></i>
                                Load Profile
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="getPermissions()">
                                <i class="fas fa-shield-alt"></i>
                                Get Permissions
                            </button>
                            <button class="btn btn-sm btn-info" onclick="clearProfileForm()">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>

                        <form id="profileForm" class="auth-form">
                            <div class="form-group">
                                <label class="form-label" for="profileEmail">Email Address</label>
                                <input type="email" id="profileEmail" name="email" class="form-input" readonly>
                                <small class="text-muted">Email cannot be changed here. Use the Change Email feature.</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="profileUsername">Username</label>
                                <input type="text" id="profileUsername" name="username" class="form-input" readonly>
                                <small class="text-muted">Username cannot be changed after registration.</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="profileFirstName">First Name</label>
                                <input type="text" id="profileFirstName" name="firstName" class="form-input" placeholder="Enter first name">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="profileLastName">Last Name</label>
                                <input type="text" id="profileLastName" name="lastName" class="form-input" placeholder="Enter last name">
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="profileBio">Bio</label>
                                <textarea id="profileBio" name="bio" class="form-input form-textarea" placeholder="Tell us about yourself" rows="3"></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <i class="fas fa-save"></i>
                                Update Profile
                            </button>
                        </form>

                        <div id="profileResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Password Tab -->
            <div class="tab-content" id="password-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Change Password</h3>
                        <p>Update your account password securely</p>
                    </div>
                    <div class="card-body">
                        <form id="passwordForm" class="auth-form">
                            <div class="form-group">
                                <label class="form-label" for="currentPassword">Current Password</label>
                                <input type="password" id="currentPassword" name="currentPassword" class="form-input" placeholder="Enter current password" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="newPassword">New Password</label>
                                <input type="password" id="newPassword" name="newPassword" class="form-input" placeholder="Enter new password" required>
                                <div class="password-strength" id="newPasswordStrength" style="margin-top: 0.5rem; display: none;">
                                    <div class="strength-bar" style="height: 4px; background: var(--bg-tertiary); border-radius: 2px;">
                                        <div class="strength-fill" style="height: 100%; border-radius: 2px; transition: all 0.3s ease; width: 0%;"></div>
                                    </div>
                                    <div class="strength-text" style="font-size: 0.75rem; margin-top: 0.25rem;"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="confirmNewPassword">Confirm New Password</label>
                                <input type="password" id="confirmNewPassword" name="confirmNewPassword" class="form-input" placeholder="Confirm new password" required>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <i class="fas fa-key"></i>
                                Change Password
                            </button>
                        </form>

                        <div id="passwordResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Forgot Password Tab -->
            <div class="tab-content" id="forgot-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Forgot Password</h3>
                        <p>Request password reset link via email</p>
                    </div>
                    <div class="card-body">
                        <form id="forgotForm" class="auth-form">
                            <div class="form-group">
                                <label class="form-label" for="forgotEmail">Email Address</label>
                                <input type="email" id="forgotEmail" name="email" class="form-input" placeholder="Enter your email address" required>
                            </div>

                            <button type="submit" class="btn btn-primary" style="width: 100%;">
                                <i class="fas fa-paper-plane"></i>
                                Send Reset Link
                            </button>
                        </form>

                        <div class="mt-4">
                            <h4>Reset Password with Token</h4>
                            <form id="resetForm" class="auth-form">
                                <div class="form-group">
                                    <label class="form-label" for="resetToken">Reset Token</label>
                                    <input type="text" id="resetToken" name="token" class="form-input" placeholder="Enter reset token from email">
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="resetNewPassword">New Password</label>
                                    <input type="password" id="resetNewPassword" name="newPassword" class="form-input" placeholder="Enter new password">
                                </div>

                                <button type="submit" class="btn btn-success" style="width: 100%;">
                                    <i class="fas fa-check"></i>
                                    Reset Password
                                </button>
                            </form>
                        </div>

                        <div id="forgotResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Email Verification Tab -->
            <div class="tab-content" id="verify-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>Email Verification</h3>
                        <p>Verify email address or resend verification email</p>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h4>Resend Verification Email</h4>
                            <form id="resendVerificationForm" class="auth-form">
                                <div class="form-group">
                                    <label class="form-label" for="verifyEmail">Email Address</label>
                                    <input type="email" id="verifyEmail" name="email" class="form-input" placeholder="Enter email address">
                                </div>

                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    <i class="fas fa-paper-plane"></i>
                                    Resend Verification
                                </button>
                            </form>
                        </div>

                        <div>
                            <h4>Verify Email with Token</h4>
                            <form id="verifyTokenForm" class="auth-form">
                                <div class="form-group">
                                    <label class="form-label" for="verificationToken">Verification Token</label>
                                    <input type="text" id="verificationToken" name="token" class="form-input" placeholder="Enter verification token from email">
                                </div>

                                <button type="submit" class="btn btn-success" style="width: 100%;">
                                    <i class="fas fa-check-circle"></i>
                                    Verify Email
                                </button>
                            </form>
                        </div>

                        <div id="verifyResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Permissions Tab -->
            <div class="tab-content" id="permissions-tab">
                <div class="card">
                    <div class="card-header">
                        <h3>User Permissions</h3>
                        <p>View user permissions and test authorization</p>
                    </div>
                    <div class="card-body">
                        <div class="quick-test-buttons">
                            <button class="btn btn-sm btn-primary" onclick="loadPermissions()">
                                <i class="fas fa-sync"></i>
                                Load Permissions
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="testPermission()">
                                <i class="fas fa-shield-alt"></i>
                                Test Permission
                            </button>
                        </div>

                        <div class="mb-4">
                            <h4>Check Specific Permission</h4>
                            <form id="permissionForm" class="auth-form">
                                <div class="form-group">
                                    <label class="form-label" for="permissionResource">Resource</label>
                                    <select id="permissionResource" name="resource" class="form-select">
                                        <option value="USER">USER</option>
                                        <option value="PROFILE">PROFILE</option>
                                        <option value="ANALYTICS">ANALYTICS</option>
                                        <option value="SECURITY">SECURITY</option>
                                        <option value="SYSTEM">SYSTEM</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label" for="permissionAction">Action</label>
                                    <select id="permissionAction" name="action" class="form-select">
                                        <option value="CREATE">CREATE</option>
                                        <option value="READ">READ</option>
                                        <option value="UPDATE">UPDATE</option>
                                        <option value="DELETE">DELETE</option>
                                        <option value="MANAGE">MANAGE</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    <i class="fas fa-check"></i>
                                    Check Permission
                                </button>
                            </form>
                        </div>

                        <div id="permissionsResponse" class="response-viewer" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../../assets/js/utils/api.js"></script>
    <script src="../../assets/js/utils/auth.js"></script>
    <script src="../../assets/js/utils/storage.js"></script>
    <script src="../../assets/js/utils/notifications.js"></script>
    <script src="../../assets/js/shared/components.js"></script>
    <script src="../../assets/js/shared/constants.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            setupTabs();
            setupForms();
            setupPasswordStrength();
        });

        async function initializeApp() {
            // Check server health
            await checkServerHealth();

            // Check authentication status
            updateAuthStatus();

            // Initialize theme
            initializeTheme();

            // Load profile if authenticated
            if (window.authManager.isAuthenticated()) {
                loadProfile();
            }
        }

        function setupTabs() {
            const tabs = document.querySelectorAll('.auth-tab');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    tab.classList.add('active');

                    // Show corresponding content
                    const tabId = tab.dataset.tab + '-tab';
                    const content = document.getElementById(tabId);
                    if (content) {
                        content.classList.add('active');
                    }
                });
            });
        }

        function setupForms() {
            // Login form
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', handleLogin);

            // Register form
            const registerForm = document.getElementById('registerForm');
            registerForm.addEventListener('submit', handleRegister);

            // Profile form
            const profileForm = document.getElementById('profileForm');
            profileForm.addEventListener('submit', handleProfileUpdate);

            // Password form
            const passwordForm = document.getElementById('passwordForm');
            passwordForm.addEventListener('submit', handlePasswordChange);

            // Forgot password form
            const forgotForm = document.getElementById('forgotForm');
            forgotForm.addEventListener('submit', handleForgotPassword);

            // Reset password form
            const resetForm = document.getElementById('resetForm');
            resetForm.addEventListener('submit', handleResetPassword);

            // Resend verification form
            const resendForm = document.getElementById('resendVerificationForm');
            resendForm.addEventListener('submit', handleResendVerification);

            // Verify token form
            const verifyForm = document.getElementById('verifyTokenForm');
            verifyForm.addEventListener('submit', handleVerifyEmail);

            // Permission form
            const permissionForm = document.getElementById('permissionForm');
            permissionForm.addEventListener('submit', handleCheckPermission);
        }

        function setupPasswordStrength() {
            const passwordInputs = ['registerPassword', 'newPassword'];

            passwordInputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                const strengthId = inputId === 'registerPassword' ? 'passwordStrength' : 'newPasswordStrength';
                const strengthElement = document.getElementById(strengthId);

                if (input && strengthElement) {
                    input.addEventListener('input', () => {
                        const strength = Utils.getPasswordStrength(input.value);
                        updatePasswordStrength(strengthElement, strength);
                    });
                }
            });
        }

        function updatePasswordStrength(element, strength) {
            const fill = element.querySelector('.strength-fill');
            const text = element.querySelector('.strength-text');

            if (strength.score === 0) {
                element.style.display = 'none';
                return;
            }

            element.style.display = 'block';
            fill.style.width = strength.percentage + '%';
            text.textContent = strength.level;

            // Color based on strength
            const colors = ['#ef4444', '#f59e0b', '#eab308', '#22c55e', '#10b981'];
            fill.style.backgroundColor = colors[strength.score - 1] || colors[0];
        }

        // Form handlers
        async function handleLogin(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('loginResponse');

            try {
                const response = await window.authManager.login({
                    email: data.email,
                    password: data.password
                });

                if (response.requiresMFA) {
                    showResponse('loginResponse', {
                        success: true,
                        message: 'MFA required. Please complete MFA verification.',
                        data: { mfaToken: response.mfaToken }
                    }, 'success');

                    // Redirect to MFA page
                    setTimeout(() => {
                        window.location.href = '../mfa/index.html';
                    }, 2000);
                } else {
                    showResponse('loginResponse', {
                        success: true,
                        message: 'Login successful!',
                        data: response
                    }, 'success');

                    updateAuthStatus();
                    loadProfile();
                }
            } catch (error) {
                showResponse('loginResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleRegister(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Validate passwords match
            if (data.password !== data.confirmPassword) {
                window.notificationManager.error('Passwords do not match');
                return;
            }

            showLoading(form);
            clearResponse('registerResponse');

            try {
                const response = await window.authManager.register({
                    email: data.email,
                    username: data.username,
                    firstName: data.firstName,
                    lastName: data.lastName,
                    password: data.password
                });

                showResponse('registerResponse', response, 'success');
                form.reset();
            } catch (error) {
                showResponse('registerResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleProfileUpdate(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('profileResponse');

            try {
                const response = await window.authManager.updateProfile({
                    firstName: data.firstName,
                    lastName: data.lastName,
                    bio: data.bio
                });

                showResponse('profileResponse', response, 'success');
                updateAuthStatus();
            } catch (error) {
                showResponse('profileResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handlePasswordChange(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Validate passwords match
            if (data.newPassword !== data.confirmNewPassword) {
                window.notificationManager.error('New passwords do not match');
                return;
            }

            showLoading(form);
            clearResponse('passwordResponse');

            try {
                const response = await window.authManager.changePassword(
                    data.currentPassword,
                    data.newPassword
                );

                showResponse('passwordResponse', response, 'success');
                form.reset();
            } catch (error) {
                showResponse('passwordResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        // Utility functions
        async function loadProfile() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const response = await window.apiClient.auth.getProfile();
                if (response.success) {
                    const user = response.data.user;
                    document.getElementById('profileEmail').value = user.email || '';
                    document.getElementById('profileUsername').value = user.username || '';
                    document.getElementById('profileFirstName').value = user.firstName || '';
                    document.getElementById('profileLastName').value = user.lastName || '';
                    document.getElementById('profileBio').value = user.bio || '';

                    showResponse('profileResponse', response, 'success');
                }
            } catch (error) {
                showResponse('profileResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        async function loadPermissions() {
            if (!window.authManager.isAuthenticated()) {
                window.notificationManager.warning('Please log in first');
                return;
            }

            try {
                const permissions = await window.authManager.getPermissions();
                showResponse('permissionsResponse', {
                    success: true,
                    message: 'User permissions loaded',
                    data: { permissions }
                }, 'success');
            } catch (error) {
                showResponse('permissionsResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            }
        }

        // Quick test functions
        function fillTestUser() {
            document.getElementById('loginEmail').value = '<EMAIL>';
            document.getElementById('loginPassword').value = 'TestPassword123!';
        }

        function fillAdminUser() {
            document.getElementById('loginEmail').value = '<EMAIL>';
            document.getElementById('loginPassword').value = 'AdminPassword123!';
        }

        function fillTestRegistration() {
            const timestamp = Date.now();
            document.getElementById('registerEmail').value = `test${timestamp}@example.com`;
            document.getElementById('registerUsername').value = `testuser${timestamp}`;
            document.getElementById('registerFirstName').value = 'Test';
            document.getElementById('registerLastName').value = 'User';
            document.getElementById('registerPassword').value = 'TestPassword123!';
            document.getElementById('registerConfirmPassword').value = 'TestPassword123!';
            document.getElementById('agreeTerms').checked = true;
        }

        // Clear form functions
        function clearLoginForm() {
            document.getElementById('loginForm').reset();
            clearResponse('loginResponse');
        }

        function clearRegisterForm() {
            document.getElementById('registerForm').reset();
            clearResponse('registerResponse');
        }

        function clearProfileForm() {
            document.getElementById('profileForm').reset();
            clearResponse('profileResponse');
        }

        // UI utility functions
        function showLoading(form) {
            const button = form.querySelector('button[type="submit"]');
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            }
        }

        function hideLoading(form) {
            const button = form.querySelector('button[type="submit"]');
            if (button) {
                button.disabled = false;
                // Restore original button text based on form
                const formId = form.id;
                const buttonTexts = {
                    'loginForm': '<i class="fas fa-sign-in-alt"></i> Login',
                    'registerForm': '<i class="fas fa-user-plus"></i> Register',
                    'profileForm': '<i class="fas fa-save"></i> Update Profile',
                    'passwordForm': '<i class="fas fa-key"></i> Change Password',
                    'forgotForm': '<i class="fas fa-paper-plane"></i> Send Reset Link',
                    'resetForm': '<i class="fas fa-check"></i> Reset Password',
                    'resendVerificationForm': '<i class="fas fa-paper-plane"></i> Resend Verification',
                    'verifyTokenForm': '<i class="fas fa-check-circle"></i> Verify Email',
                    'permissionForm': '<i class="fas fa-check"></i> Check Permission'
                };
                button.innerHTML = buttonTexts[formId] || 'Submit';
            }
        }

        function showResponse(elementId, response, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'block';
                element.innerHTML = Utils.highlightJSON(response);
                element.className = `response-viewer ${type}`;
            }
        }

        function clearResponse(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = 'none';
                element.innerHTML = '';
            }
        }

        // Shared utility functions from main page
        async function checkServerHealth() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();

                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                if (response.ok) {
                    indicator.className = 'fas fa-circle status-indicator status-online';
                    text.textContent = 'Server Online';
                } else {
                    throw new Error('Server responded with error');
                }
            } catch (error) {
                const statusElement = document.getElementById('serverStatus');
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                indicator.className = 'fas fa-circle status-indicator status-offline';
                text.textContent = 'Server Offline';
            }
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const user = JSON.parse(localStorage.getItem('user') || 'null');

            const authElement = document.getElementById('authStatus');
            const text = authElement.querySelector('.auth-text');

            if (token && user) {
                text.textContent = `Logged in as ${user.email}`;
                authElement.classList.add('authenticated');
            } else {
                text.textContent = 'Not Authenticated';
                authElement.classList.remove('authenticated');
            }
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const themeToggle = document.getElementById('themeToggle');
            const icon = themeToggle.querySelector('i');

            if (savedTheme === 'dark') {
                icon.className = 'fas fa-sun';
            } else {
                icon.className = 'fas fa-moon';
            }

            themeToggle.addEventListener('click', toggleTheme);
        }

        function toggleTheme() {
            const currentTheme = document.body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = document.getElementById('themeToggle').querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }
    </script>
</body>
</html>

        async function handleRegister(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Validate passwords match
            if (data.password !== data.confirmPassword) {
                window.notificationManager.error('Passwords do not match');
                return;
            }

            showLoading(form);
            clearResponse('registerResponse');

            try {
                const response = await window.authManager.register({
                    email: data.email,
                    username: data.username,
                    firstName: data.firstName,
                    lastName: data.lastName,
                    password: data.password
                });

                showResponse('registerResponse', response, 'success');
                form.reset();
            } catch (error) {
                showResponse('registerResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleProfileUpdate(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('profileResponse');

            try {
                const response = await window.authManager.updateProfile({
                    firstName: data.firstName,
                    lastName: data.lastName,
                    bio: data.bio
                });

                showResponse('profileResponse', response, 'success');
                updateAuthStatus();
            } catch (error) {
                showResponse('profileResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handlePasswordChange(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Validate passwords match
            if (data.newPassword !== data.confirmNewPassword) {
                window.notificationManager.error('New passwords do not match');
                return;
            }

            showLoading(form);
            clearResponse('passwordResponse');

            try {
                const response = await window.authManager.changePassword(
                    data.currentPassword,
                    data.newPassword
                );

                showResponse('passwordResponse', response, 'success');
                form.reset();
            } catch (error) {
                showResponse('passwordResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleForgotPassword(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('forgotResponse');

            try {
                const response = await window.authManager.forgotPassword(data.email);
                showResponse('forgotResponse', response, 'success');
            } catch (error) {
                showResponse('forgotResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleResetPassword(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('forgotResponse');

            try {
                const response = await window.authManager.resetPassword(
                    data.token,
                    data.newPassword
                );

                showResponse('forgotResponse', response, 'success');
                form.reset();
            } catch (error) {
                showResponse('forgotResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleResendVerification(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('verifyResponse');

            try {
                const response = await window.authManager.resendEmailVerification(data.email);
                showResponse('verifyResponse', response, 'success');
            } catch (error) {
                showResponse('verifyResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleVerifyEmail(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('verifyResponse');

            try {
                const response = await window.authManager.verifyEmail(data.token);
                showResponse('verifyResponse', response, 'success');
                updateAuthStatus();
            } catch (error) {
                showResponse('verifyResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }

        async function handleCheckPermission(e) {
            e.preventDefault();
            const form = e.target;
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            showLoading(form);
            clearResponse('permissionsResponse');

            try {
                const hasPermission = await window.authManager.checkPermission(
                    data.resource,
                    data.action
                );

                showResponse('permissionsResponse', {
                    success: true,
                    message: `Permission check: ${data.resource}:${data.action}`,
                    data: { hasPermission }
                }, hasPermission ? 'success' : 'error');
            } catch (error) {
                showResponse('permissionsResponse', {
                    success: false,
                    error: error.message,
                    details: error.details
                }, 'error');
            } finally {
                hideLoading(form);
            }
        }