/**
 * Storage Manager - Handles localStorage operations with encryption and expiration
 */

class StorageManager {
    constructor() {
        this.prefix = 'secure_backend_';
        this.encryptionKey = this.getOrCreateEncryptionKey();
    }

    /**
     * Get or create encryption key for sensitive data
     */
    getOrCreateEncryptionKey() {
        let key = localStorage.getItem(this.prefix + 'encryption_key');
        if (!key) {
            key = this.generateRandomKey();
            localStorage.setItem(this.prefix + 'encryption_key', key);
        }
        return key;
    }

    /**
     * Generate random encryption key
     */
    generateRandomKey() {
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }

    /**
     * Simple XOR encryption for sensitive data
     */
    encrypt(data, key) {
        const dataStr = JSON.stringify(data);
        let encrypted = '';
        for (let i = 0; i < dataStr.length; i++) {
            encrypted += String.fromCharCode(
                dataStr.charCodeAt(i) ^ key.charCodeAt(i % key.length)
            );
        }
        return btoa(encrypted);
    }

    /**
     * Simple XOR decryption for sensitive data
     */
    decrypt(encryptedData, key) {
        try {
            const encrypted = atob(encryptedData);
            let decrypted = '';
            for (let i = 0; i < encrypted.length; i++) {
                decrypted += String.fromCharCode(
                    encrypted.charCodeAt(i) ^ key.charCodeAt(i % key.length)
                );
            }
            return JSON.parse(decrypted);
        } catch (error) {
            console.warn('Failed to decrypt data:', error);
            return null;
        }
    }

    /**
     * Set item with optional expiration and encryption
     */
    setItem(key, value, options = {}) {
        const {
            expires = null, // Expiration time in milliseconds
            encrypt = false, // Whether to encrypt the data
            sensitive = false // Mark as sensitive data
        } = options;

        const storageKey = this.prefix + key;
        const storageValue = {
            data: encrypt || sensitive ? this.encrypt(value, this.encryptionKey) : value,
            timestamp: Date.now(),
            expires: expires ? Date.now() + expires : null,
            encrypted: encrypt || sensitive,
            sensitive
        };

        try {
            localStorage.setItem(storageKey, JSON.stringify(storageValue));
            return true;
        } catch (error) {
            console.error('Failed to set storage item:', error);
            return false;
        }
    }

    /**
     * Get item with automatic expiration check and decryption
     */
    getItem(key, defaultValue = null) {
        const storageKey = this.prefix + key;

        try {
            const item = localStorage.getItem(storageKey);
            if (!item) return defaultValue;

            const storageValue = JSON.parse(item);

            // Check expiration
            if (storageValue.expires && Date.now() > storageValue.expires) {
                this.removeItem(key);
                return defaultValue;
            }

            // Decrypt if necessary
            if (storageValue.encrypted) {
                const decryptedData = this.decrypt(storageValue.data, this.encryptionKey);
                return decryptedData !== null ? decryptedData : defaultValue;
            }

            return storageValue.data;
        } catch (error) {
            console.error('Failed to get storage item:', error);
            return defaultValue;
        }
    }

    /**
     * Remove item from storage
     */
    removeItem(key) {
        const storageKey = this.prefix + key;
        localStorage.removeItem(storageKey);
    }

    /**
     * Clear all items with our prefix
     */
    clear() {
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                keysToRemove.push(key);
            }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }

    /**
     * Get all keys with our prefix
     */
    getAllKeys() {
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                keys.push(key.substring(this.prefix.length));
            }
        }
        return keys;
    }

    /**
     * Check if item exists and is not expired
     */
    hasItem(key) {
        return this.getItem(key) !== null;
    }

    /**
     * Get storage usage information
     */
    getStorageInfo() {
        let totalSize = 0;
        let itemCount = 0;
        const items = {};

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.prefix)) {
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                totalSize += size;
                itemCount++;

                const cleanKey = key.substring(this.prefix.length);
                items[cleanKey] = {
                    size,
                    lastModified: this.getItem(cleanKey + '_timestamp') || 'Unknown'
                };
            }
        }

        return {
            totalSize,
            itemCount,
            items,
            maxSize: 5 * 1024 * 1024 // 5MB typical localStorage limit
        };
    }

    /**
     * Clean up expired items
     */
    cleanup() {
        const keys = this.getAllKeys();
        let cleanedCount = 0;

        keys.forEach(key => {
            const item = this.getItem(key);
            if (item === null) {
                cleanedCount++;
            }
        });

        return cleanedCount;
    }

    /**
     * Export data for backup
     */
    exportData() {
        const data = {};
        const keys = this.getAllKeys();

        keys.forEach(key => {
            const value = this.getItem(key);
            if (value !== null) {
                data[key] = value;
            }
        });

        return {
            timestamp: new Date().toISOString(),
            data
        };
    }

    /**
     * Import data from backup
     */
    importData(backupData, options = {}) {
        const { overwrite = false, skipSensitive = true } = options;

        if (!backupData.data) {
            throw new Error('Invalid backup data format');
        }

        let importedCount = 0;
        let skippedCount = 0;

        Object.entries(backupData.data).forEach(([key, value]) => {
            // Skip sensitive data if requested
            if (skipSensitive && key.includes('token')) {
                skippedCount++;
                return;
            }

            // Skip existing items if not overwriting
            if (!overwrite && this.hasItem(key)) {
                skippedCount++;
                return;
            }

            this.setItem(key, value);
            importedCount++;
        });

        return { importedCount, skippedCount };
    }
}

// Create global storage manager instance
window.storageManager = new StorageManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StorageManager;
}