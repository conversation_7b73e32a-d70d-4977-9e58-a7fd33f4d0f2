/**
 * API Client - Centralized HTTP client for backend API
 * Handles authentication, request/response logging, and error handling
 */

class APIClient {
    constructor(baseURL = 'http://localhost:3000/api/v1') {
        this.baseURL = baseURL;
        this.healthURL = 'http://localhost:3000/health';
        this.token = localStorage.getItem('auth_token');
        this.refreshToken = localStorage.getItem('refresh_token');

        // Request/Response logging
        this.requestLog = JSON.parse(localStorage.getItem('apiRequestLog') || '[]');

        // Initialize auth methods
        this.auth = {
            register: this.register.bind(this),
            login: this.login.bind(this),
            logout: this.logout.bind(this),
            refresh: this.refreshAuthToken.bind(this),
            forgotPassword: this.forgotPassword.bind(this),
            resetPassword: this.resetPassword.bind(this),
            verifyEmail: this.verifyEmail.bind(this),
            resendVerification: this.resendVerification.bind(this),
            getProfile: this.getProfile.bind(this),
            updateProfile: this.updateProfile.bind(this),
            changePassword: this.changePassword.bind(this),
            changeEmail: this.changeEmail.bind(this),
            getPermissions: this.getPermissions.bind(this),
            checkPermission: this.checkPermission.bind(this)
        };

        // Initialize MFA methods
        this.mfa = {
            initializeSetup: this.mfaInitializeSetup.bind(this),
            completeSetup: this.mfaCompleteSetup.bind(this),
            verifyTotp: this.mfaVerifyTotp.bind(this),
            getStatus: this.mfaGetStatus.bind(this),
            disable: this.mfaDisable.bind(this),
            verifyBackupCode: this.mfaVerifyBackupCode.bind(this),
            regenerateBackupCodes: this.mfaRegenerateBackupCodes.bind(this)
        };

        // Initialize session methods
        this.sessions = {
            list: this.sessionsList.bind(this),
            current: this.sessionsCurrent.bind(this),
            terminate: this.sessionsTerminate.bind(this),
            terminateAllOthers: this.sessionsTerminateAllOthers.bind(this),
            trustDevice: this.sessionsTrustDevice.bind(this),
            getActivity: this.sessionsGetActivity.bind(this),
            getTrustedDevices: this.sessionsGetTrustedDevices.bind(this)
        };

        // Initialize user management methods
        this.users = {
            list: this.usersList.bind(this),
            get: this.usersGet.bind(this),
            update: this.usersUpdate.bind(this),
            delete: this.usersDelete.bind(this),
            search: this.usersSearch.bind(this),
            getByRole: this.usersGetByRole.bind(this),
            getStats: this.usersGetStats.bind(this),
            updateRole: this.usersUpdateRole.bind(this),
            getByEmail: this.usersGetByEmail.bind(this)
        };

        // Initialize security methods
        this.security = {
            getStatus: this.securityGetStatus.bind(this),
            getDdosMetrics: this.securityGetDdosMetrics.bind(this),
            blockIP: this.securityBlockIP.bind(this),
            unblockIP: this.securityUnblockIP.bind(this),
            getBlockedIPs: this.securityGetBlockedIPs.bind(this)
        };

        // Initialize OAuth methods
        this.oauth = {
            initiate: this.oauthInitiate.bind(this),
            getAccounts: this.oauthGetAccounts.bind(this),
            link: this.oauthLink.bind(this),
            unlink: this.oauthUnlink.bind(this)
        };

        // Initialize email verification methods
        this.emailVerification = {
            request: this.emailVerificationRequest.bind(this),
            verify: this.emailVerificationVerify.bind(this)
        };
    }

    /**
     * Core request method with authentication, logging, and error handling
     */
    async request(endpoint, options = {}) {
        const url = endpoint.startsWith('http') ? endpoint : `${this.baseURL}${endpoint}`;
        const startTime = Date.now();

        // Default headers
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        // Add authentication token if available
        if (this.token && !options.skipAuth) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        // Add MFA token if provided
        if (options.mfaToken) {
            headers['X-MFA-Token'] = options.mfaToken;
        }

        const requestConfig = {
            method: options.method || 'GET',
            headers,
            ...options
        };

        // Add body for non-GET requests
        if (options.body && requestConfig.method !== 'GET') {
            requestConfig.body = typeof options.body === 'string'
                ? options.body
                : JSON.stringify(options.body);
        }

        // Log request
        const requestLogEntry = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            method: requestConfig.method,
            url,
            headers: { ...headers },
            body: options.body,
            startTime
        };

        // Remove sensitive data from logs
        if (requestLogEntry.headers.Authorization) {
            requestLogEntry.headers.Authorization = 'Bearer [REDACTED]';
        }
        if (requestLogEntry.headers['X-MFA-Token']) {
            requestLogEntry.headers['X-MFA-Token'] = '[REDACTED]';
        }

        try {
            const response = await fetch(url, requestConfig);
            const endTime = Date.now();
            const duration = endTime - startTime;

            let responseData;
            const contentType = response.headers.get('content-type');

            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            } else {
                responseData = await response.text();
            }

            // Complete request log entry
            requestLogEntry.response = {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                data: responseData,
                duration
            };

            // Add to request log
            this.addToRequestLog(requestLogEntry);

            // Handle token refresh for 401 errors
            if (response.status === 401 && this.refreshToken && !options.skipRefresh) {
                try {
                    await this.refreshAuthToken();
                    // Retry original request with new token
                    return this.request(endpoint, { ...options, skipRefresh: true });
                } catch (refreshError) {
                    this.clearAuthTokens();
                    throw new APIError('Authentication failed', 401, refreshError);
                }
            }

            if (!response.ok) {
                throw new APIError(
                    responseData.error || responseData.message || `HTTP ${response.status}`,
                    response.status,
                    responseData
                );
            }

            return responseData;

        } catch (error) {
            const endTime = Date.now();
            const duration = endTime - startTime;

            // Complete request log entry with error
            requestLogEntry.response = {
                error: error.message,
                duration
            };

            this.addToRequestLog(requestLogEntry);

            if (error instanceof APIError) {
                throw error;
            }

            throw new APIError(
                error.message || 'Network error',
                0,
                { originalError: error }
            );
        }
    }

    /**
     * Utility methods
     */
    addToRequestLog(entry) {
        this.requestLog.unshift(entry);

        // Keep only last 100 requests
        if (this.requestLog.length > 100) {
            this.requestLog = this.requestLog.slice(0, 100);
        }

        localStorage.setItem('apiRequestLog', JSON.stringify(this.requestLog));

        // Dispatch custom event for UI updates
        window.dispatchEvent(new CustomEvent('apiRequest', { detail: entry }));
    }

    getRequestLog() {
        return this.requestLog;
    }

    clearRequestLog() {
        this.requestLog = [];
        localStorage.removeItem('apiRequestLog');
    }

    setAuthTokens(token, refreshToken) {
        this.token = token;
        this.refreshToken = refreshToken;
        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', refreshToken);
    }

    clearAuthTokens() {
        this.token = null;
        this.refreshToken = null;
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
    }

    /**
     * Health check
     */
    async checkHealth() {
        return this.request(this.healthURL, { skipAuth: true });
    }

    /**
     * Authentication methods
     */
    async register(userData) {
        return this.request('/auth/register', {
            method: 'POST',
            body: userData,
            skipAuth: true
        });
    }

    async login(credentials) {
        const response = await this.request('/auth/login', {
            method: 'POST',
            body: credentials,
            skipAuth: true
        });

        // Store tokens if login successful and no MFA required
        if (response.success && !response.data.requiresMFA) {
            this.setAuthTokens(response.data.token, response.data.refreshToken);
            localStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response;
    }

    async logout() {
        try {
            await this.request('/auth/logout', { method: 'POST' });
        } finally {
            this.clearAuthTokens();
        }
    }

    async refreshAuthToken() {
        const response = await this.request('/auth/refresh', {
            method: 'POST',
            body: { refreshToken: this.refreshToken },
            skipAuth: true,
            skipRefresh: true
        });

        if (response.success) {
            this.setAuthTokens(response.data.token, response.data.refreshToken);
        }

        return response;
    }

    async forgotPassword(emailOrUsername) {
        return this.request('/auth/forgot-password', {
            method: 'POST',
            body: { email: emailOrUsername },
            skipAuth: true
        });
    }

    async resetPassword(token, newPassword) {
        return this.request('/auth/reset-password', {
            method: 'POST',
            body: { token, newPassword },
            skipAuth: true
        });
    }

    async verifyEmail(token) {
        return this.request('/auth/verify-email', {
            method: 'POST',
            body: { token },
            skipAuth: true
        });
    }

    async resendVerification(email) {
        return this.request('/auth/resend-verification', {
            method: 'POST',
            body: { email },
            skipAuth: true
        });
    }

    async getProfile() {
        return this.request('/auth/profile');
    }

    async updateProfile(profileData) {
        return this.request('/auth/profile', {
            method: 'PATCH',
            body: profileData
        });
    }

    async changePassword(currentPassword, newPassword) {
        return this.request('/auth/change-password', {
            method: 'PATCH',
            body: { currentPassword, newPassword }
        });
    }

    async changeEmail(newEmail, password) {
        return this.request('/auth/change-email', {
            method: 'POST',
            body: { newEmail, password }
        });
    }

    async getPermissions() {
        return this.request('/auth/permissions');
    }

    async checkPermission(resource, action) {
        return this.request(`/auth/check-permission/${resource}/${action}`);
    }

    /**
     * MFA methods
     */
    async mfaInitializeSetup() {
        return this.request('/mfa/setup/initialize', { method: 'POST' });
    }

    async mfaCompleteSetup(setupToken, totpCode) {
        return this.request('/mfa/setup/complete', {
            method: 'POST',
            body: { setupToken, totpCode }
        });
    }

    async mfaVerifyTotp(totpCode, mfaToken) {
        const response = await this.request('/mfa/verify/totp', {
            method: 'POST',
            body: { totpCode },
            mfaToken,
            skipAuth: true
        });

        // Store tokens if MFA verification successful
        if (response.success && response.data.token) {
            this.setAuthTokens(response.data.token, response.data.refreshToken);
            localStorage.setItem('user', JSON.stringify(response.data.user));
        }

        return response;
    }

    async mfaGetStatus() {
        return this.request('/mfa/status');
    }

    async mfaDisable(totpCode) {
        return this.request('/mfa/disable', {
            method: 'POST',
            body: { totpCode }
        });
    }

    async mfaVerifyBackupCode(backupCode) {
        return this.request('/mfa/verify/backup-code', {
            method: 'POST',
            body: { backupCode }
        });
    }

    async mfaRegenerateBackupCodes(totpCode) {
        return this.request('/mfa/backup-codes/regenerate', {
            method: 'POST',
            body: { totpCode }
        });
    }

    /**
     * Session management methods
     */
    async sessionsList(limit = 50, offset = 0) {
        return this.request(`/sessions?limit=${limit}&offset=${offset}`);
    }

    async sessionsCurrent() {
        return this.request('/sessions/current');
    }

    async sessionsTerminate(sessionId) {
        return this.request('/sessions/terminate', {
            method: 'POST',
            body: { sessionId }
        });
    }

    async sessionsTerminateAllOthers() {
        return this.request('/sessions/terminate-all-others', { method: 'POST' });
    }

    async sessionsTrustDevice() {
        return this.request('/sessions/trust-device', { method: 'POST' });
    }

    async sessionsGetActivity(limit = 50, offset = 0) {
        return this.request(`/sessions/activity?limit=${limit}&offset=${offset}`);
    }

    async sessionsGetTrustedDevices() {
        return this.request('/sessions/trusted-devices');
    }

    /**
     * User management methods
     */
    async usersList(limit = 50, offset = 0, filters = {}) {
        const params = new URLSearchParams({ limit, offset, ...filters });
        return this.request(`/users?${params}`);
    }

    async usersGet(userId) {
        return this.request(`/users/${userId}`);
    }

    async usersUpdate(userId, userData) {
        return this.request(`/users/${userId}`, {
            method: 'PUT',
            body: userData
        });
    }

    async usersDelete(userId) {
        return this.request(`/users/${userId}`, { method: 'DELETE' });
    }

    async usersSearch(query) {
        return this.request(`/users/search?q=${encodeURIComponent(query)}`);
    }

    async usersGetByRole(role) {
        return this.request(`/users/role/${role}`);
    }

    async usersGetStats() {
        return this.request('/users/admin/stats');
    }

    async usersUpdateRole(userId, role) {
        return this.request(`/users/${userId}/role`, {
            method: 'PATCH',
            body: { role }
        });
    }

    async usersGetByEmail(email) {
        return this.request(`/users/email/${encodeURIComponent(email)}`);
    }

    /**
     * Security methods
     */
    async securityGetStatus() {
        return this.request('/security/status');
    }

    async securityGetDdosMetrics(hours = 24) {
        return this.request(`/security/ddos/metrics?hours=${hours}`);
    }

    async securityBlockIP(ipAddress, reason) {
        return this.request('/security/block-ip', {
            method: 'POST',
            body: { ipAddress, reason }
        });
    }

    async securityUnblockIP(ipAddress) {
        return this.request('/security/unblock-ip', {
            method: 'POST',
            body: { ipAddress }
        });
    }

    async securityGetBlockedIPs() {
        return this.request('/security/blocked-ips');
    }

    /**
     * OAuth methods
     */
    async oauthInitiate(provider) {
        // This will redirect, so we don't use the normal request method
        window.location.href = `${this.baseURL}/auth/oauth/${provider}`;
    }

    async oauthGetAccounts() {
        return this.request('/auth/oauth/accounts');
    }

    async oauthLink(provider) {
        // This will redirect, so we don't use the normal request method
        window.location.href = `${this.baseURL}/auth/oauth/link/${provider}`;
    }

    async oauthUnlink(provider) {
        return this.request(`/auth/oauth/unlink/${provider}`, { method: 'DELETE' });
    }

    /**
     * Email verification methods
     */
    async emailVerificationRequest() {
        return this.request('/email-verification/request', { method: 'POST' });
    }

    async emailVerificationVerify(token) {
        return this.request(`/email-verification/verify/${token}`, { skipAuth: true });
    }
}

/**
 * Custom API Error class
 */
class APIError extends Error {
    constructor(message, statusCode = 0, details = null) {
        super(message);
        this.name = 'APIError';
        this.statusCode = statusCode;
        this.details = details;
    }
}

// Create global API client instance
window.apiClient = new APIClient();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIClient, APIError };
}