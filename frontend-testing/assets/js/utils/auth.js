/**
 * Authentication Manager - Handles user authentication state and operations
 */

class AuthManager {
    constructor() {
        this.token = localStorage.getItem('auth_token');
        this.refreshToken = localStorage.getItem('refresh_token');
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        this.mfaToken = null;

        // Event listeners for auth state changes
        this.listeners = [];

        // Auto-refresh token before expiration
        this.setupTokenRefresh();
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!(this.token && this.user);
    }

    /**
     * Check if user has MFA enabled
     */
    hasMFAEnabled() {
        return this.user && this.user.mfaEnabled;
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.user;
    }

    /**
     * Get current token
     */
    getToken() {
        return this.token;
    }

    /**
     * Get user role
     */
    getUserRole() {
        return this.user ? this.user.role : null;
    }

    /**
     * Check if user has specific role
     */
    hasRole(role) {
        return this.getUserRole() === role;
    }

    /**
     * Check if user is admin
     */
    isAdmin() {
        return this.hasRole('ADMIN');
    }

    /**
     * Login user
     */
    async login(credentials) {
        try {
            const response = await window.apiClient.auth.login(credentials);

            if (response.success) {
                if (response.data.requiresMFA) {
                    // Store MFA token for verification
                    this.mfaToken = response.data.mfaToken;
                    this.notifyListeners('mfa_required', response.data);
                    return { requiresMFA: true, mfaToken: response.data.mfaToken };
                } else {
                    // Complete login
                    this.updateAuthState(response.data.user, response.data.token, response.data.refreshToken);
                    this.notifyListeners('login_success', response.data);
                    return { success: true, user: response.data.user };
                }
            }

            return response;
        } catch (error) {
            this.notifyListeners('login_error', error);
            throw error;
        }
    }

    /**
     * Verify MFA TOTP code
     */
    async verifyMFA(totpCode) {
        try {
            const response = await window.apiClient.mfa.verifyTotp(totpCode, this.mfaToken);

            if (response.success) {
                this.updateAuthState(response.data.user, response.data.token, response.data.refreshToken);
                this.mfaToken = null;
                this.notifyListeners('mfa_success', response.data);
                return { success: true, user: response.data.user };
            }

            return response;
        } catch (error) {
            this.notifyListeners('mfa_error', error);
            throw error;
        }
    }

    /**
     * Register new user
     */
    async register(userData) {
        try {
            const response = await window.apiClient.auth.register(userData);
            this.notifyListeners('register_success', response.data);
            return response;
        } catch (error) {
            this.notifyListeners('register_error', error);
            throw error;
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            await window.apiClient.auth.logout();
        } catch (error) {
            console.warn('Logout request failed:', error);
        } finally {
            this.clearAuthState();
            this.notifyListeners('logout', null);
        }
    }

    /**
     * Refresh authentication token
     */
    async refreshAuthToken() {
        try {
            const response = await window.apiClient.auth.refresh();

            if (response.success) {
                this.token = response.data.token;
                this.refreshToken = response.data.refreshToken;
                localStorage.setItem('auth_token', this.token);
                localStorage.setItem('refresh_token', this.refreshToken);
                this.notifyListeners('token_refreshed', response.data);
                return true;
            }

            return false;
        } catch (error) {
            this.clearAuthState();
            this.notifyListeners('token_refresh_failed', error);
            return false;
        }
    }

    /**
     * Update user profile
     */
    async updateProfile(profileData) {
        try {
            const response = await window.apiClient.auth.updateProfile(profileData);

            if (response.success) {
                this.user = { ...this.user, ...response.data.user };
                localStorage.setItem('user', JSON.stringify(this.user));
                this.notifyListeners('profile_updated', this.user);
            }

            return response;
        } catch (error) {
            this.notifyListeners('profile_update_error', error);
            throw error;
        }
    }

    /**
     * Change password
     */
    async changePassword(currentPassword, newPassword) {
        try {
            const response = await window.apiClient.auth.changePassword(currentPassword, newPassword);
            this.notifyListeners('password_changed', response);
            return response;
        } catch (error) {
            this.notifyListeners('password_change_error', error);
            throw error;
        }
    }

    /**
     * Forgot password
     */
    async forgotPassword(email) {
        try {
            const response = await window.apiClient.auth.forgotPassword(email);
            this.notifyListeners('forgot_password_sent', response);
            return response;
        } catch (error) {
            this.notifyListeners('forgot_password_error', error);
            throw error;
        }
    }

    /**
     * Reset password
     */
    async resetPassword(token, newPassword) {
        try {
            const response = await window.apiClient.auth.resetPassword(token, newPassword);
            this.notifyListeners('password_reset_success', response);
            return response;
        } catch (error) {
            this.notifyListeners('password_reset_error', error);
            throw error;
        }
    }

    /**
     * Verify email
     */
    async verifyEmail(token) {
        try {
            const response = await window.apiClient.auth.verifyEmail(token);

            if (response.success && this.user) {
                this.user.emailVerified = new Date().toISOString();
                localStorage.setItem('user', JSON.stringify(this.user));
            }

            this.notifyListeners('email_verified', response);
            return response;
        } catch (error) {
            this.notifyListeners('email_verification_error', error);
            throw error;
        }
    }

    /**
     * Resend email verification
     */
    async resendEmailVerification(email) {
        try {
            const response = await window.apiClient.auth.resendVerification(email);
            this.notifyListeners('verification_email_sent', response);
            return response;
        } catch (error) {
            this.notifyListeners('verification_email_error', error);
            throw error;
        }
    }

    /**
     * Get user permissions
     */
    async getPermissions() {
        try {
            const response = await window.apiClient.auth.getPermissions();
            return response.data.permissions;
        } catch (error) {
            console.warn('Failed to get permissions:', error);
            return [];
        }
    }

    /**
     * Check specific permission
     */
    async checkPermission(resource, action) {
        try {
            const response = await window.apiClient.auth.checkPermission(resource, action);
            return response.data.hasPermission;
        } catch (error) {
            console.warn('Failed to check permission:', error);
            return false;
        }
    }

    /**
     * Update authentication state
     */
    updateAuthState(user, token, refreshToken) {
        this.user = user;
        this.token = token;
        this.refreshToken = refreshToken;

        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', refreshToken);

        // Update API client token
        window.apiClient.setAuthTokens(token, refreshToken);

        this.setupTokenRefresh();
    }

    /**
     * Clear authentication state
     */
    clearAuthState() {
        this.user = null;
        this.token = null;
        this.refreshToken = null;
        this.mfaToken = null;

        localStorage.removeItem('user');
        localStorage.removeItem('auth_token');
        localStorage.removeItem('refresh_token');

        // Clear API client tokens
        window.apiClient.clearAuthTokens();

        this.clearTokenRefresh();
    }

    /**
     * Setup automatic token refresh
     */
    setupTokenRefresh() {
        this.clearTokenRefresh();

        if (!this.token) return;

        try {
            // Decode JWT to get expiration time
            const payload = JSON.parse(atob(this.token.split('.')[1]));
            const expirationTime = payload.exp * 1000; // Convert to milliseconds
            const currentTime = Date.now();
            const timeUntilExpiry = expirationTime - currentTime;

            // Refresh token 5 minutes before expiration
            const refreshTime = Math.max(timeUntilExpiry - (5 * 60 * 1000), 60000); // At least 1 minute

            if (refreshTime > 0) {
                this.refreshTimeout = setTimeout(() => {
                    this.refreshAuthToken();
                }, refreshTime);
            }
        } catch (error) {
            console.warn('Failed to setup token refresh:', error);
        }
    }

    /**
     * Clear token refresh timeout
     */
    clearTokenRefresh() {
        if (this.refreshTimeout) {
            clearTimeout(this.refreshTimeout);
            this.refreshTimeout = null;
        }
    }

    /**
     * Add event listener for auth state changes
     */
    addEventListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * Remove event listener
     */
    removeEventListener(callback) {
        this.listeners = this.listeners.filter(listener => listener !== callback);
    }

    /**
     * Notify all listeners of auth state changes
     */
    notifyListeners(event, data) {
        this.listeners.forEach(callback => {
            try {
                callback(event, data);
            } catch (error) {
                console.error('Auth listener error:', error);
            }
        });

        // Also dispatch DOM event
        window.dispatchEvent(new CustomEvent(`auth:${event}`, { detail: data }));
    }

    /**
     * Initialize auth manager
     */
    init() {
        // Check if tokens are still valid
        if (this.token && this.user) {
            this.setupTokenRefresh();
            this.notifyListeners('init', { user: this.user, authenticated: true });
        } else {
            this.clearAuthState();
            this.notifyListeners('init', { authenticated: false });
        }
    }
}

// Create global auth manager instance
window.authManager = new AuthManager();

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    window.authManager.init();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}