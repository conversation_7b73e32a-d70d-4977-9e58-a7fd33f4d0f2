/**
 * Notification Manager - Handles toast notifications and alerts
 */

class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 seconds

        this.init();
    }

    /**
     * Initialize notification system
     */
    init() {
        // Create notification container
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        this.container.innerHTML = `
            <style>
                .notification-container {
                    position: fixed;
                    top: 1rem;
                    right: 1rem;
                    z-index: 9999;
                    pointer-events: none;
                }

                .notification {
                    background: var(--bg-card);
                    border: 1px solid var(--border-color);
                    border-radius: 0.5rem;
                    box-shadow: var(--shadow-lg);
                    padding: 1rem;
                    margin-bottom: 0.5rem;
                    min-width: 300px;
                    max-width: 400px;
                    pointer-events: auto;
                    transform: translateX(100%);
                    opacity: 0;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: flex-start;
                    gap: 0.75rem;
                }

                .notification.show {
                    transform: translateX(0);
                    opacity: 1;
                }

                .notification.hide {
                    transform: translateX(100%);
                    opacity: 0;
                }

                .notification-icon {
                    font-size: 1.25rem;
                    flex-shrink: 0;
                    margin-top: 0.125rem;
                }

                .notification-content {
                    flex: 1;
                }

                .notification-title {
                    font-weight: 600;
                    margin-bottom: 0.25rem;
                    color: var(--text-primary);
                }

                .notification-message {
                    font-size: 0.875rem;
                    color: var(--text-secondary);
                    line-height: 1.4;
                }

                .notification-close {
                    background: none;
                    border: none;
                    color: var(--text-muted);
                    cursor: pointer;
                    padding: 0;
                    font-size: 1rem;
                    flex-shrink: 0;
                    width: 1.5rem;
                    height: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 0.25rem;
                    transition: all 0.2s ease;
                }

                .notification-close:hover {
                    background-color: var(--bg-secondary);
                    color: var(--text-primary);
                }

                .notification-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 3px;
                    background-color: currentColor;
                    border-radius: 0 0 0.5rem 0.5rem;
                    transition: width linear;
                }

                /* Notification Types */
                .notification-success {
                    border-left: 4px solid var(--success-color);
                }

                .notification-success .notification-icon {
                    color: var(--success-color);
                }

                .notification-success .notification-progress {
                    background-color: var(--success-color);
                }

                .notification-error {
                    border-left: 4px solid var(--error-color);
                }

                .notification-error .notification-icon {
                    color: var(--error-color);
                }

                .notification-error .notification-progress {
                    background-color: var(--error-color);
                }

                .notification-warning {
                    border-left: 4px solid var(--warning-color);
                }

                .notification-warning .notification-icon {
                    color: var(--warning-color);
                }

                .notification-warning .notification-progress {
                    background-color: var(--warning-color);
                }

                .notification-info {
                    border-left: 4px solid var(--info-color);
                }

                .notification-info .notification-icon {
                    color: var(--info-color);
                }

                .notification-info .notification-progress {
                    background-color: var(--info-color);
                }

                @media (max-width: 768px) {
                    .notification-container {
                        top: 0.5rem;
                        right: 0.5rem;
                        left: 0.5rem;
                    }

                    .notification {
                        min-width: auto;
                        max-width: none;
                    }
                }
            </style>
        `;

        document.body.appendChild(this.container);
    }

    /**
     * Show notification
     */
    show(message, type = 'info', options = {}) {
        const {
            title = null,
            duration = this.defaultDuration,
            persistent = false,
            actions = []
        } = options;

        // Remove oldest notification if at max capacity
        if (this.notifications.length >= this.maxNotifications) {
            this.remove(this.notifications[0].id);
        }

        const notification = this.createNotification(message, type, title, duration, persistent, actions);
        this.notifications.push(notification);

        // Add to DOM
        this.container.appendChild(notification.element);

        // Trigger show animation
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // Auto-remove if not persistent
        if (!persistent && duration > 0) {
            notification.timeout = setTimeout(() => {
                this.remove(notification.id);
            }, duration);

            // Update progress bar
            this.updateProgress(notification, duration);
        }

        return notification.id;
    }

    /**
     * Create notification element
     */
    createNotification(message, type, title, duration, persistent, actions) {
        const id = Date.now() + Math.random();
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const element = document.createElement('div');
        element.className = `notification notification-${type}`;
        element.style.position = 'relative';

        element.innerHTML = `
            <i class="notification-icon ${icons[type] || icons.info}"></i>
            <div class="notification-content">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div class="notification-message">${message}</div>
                ${actions.length > 0 ? this.createActionButtons(actions, id) : ''}
            </div>
            <button class="notification-close" onclick="window.notificationManager.remove('${id}')">
                <i class="fas fa-times"></i>
            </button>
            ${!persistent && duration > 0 ? '<div class="notification-progress"></div>' : ''}
        `;

        return {
            id,
            element,
            type,
            message,
            title,
            duration,
            persistent,
            timeout: null
        };
    }

    /**
     * Create action buttons for notification
     */
    createActionButtons(actions, notificationId) {
        const buttonsHtml = actions.map(action => `
            <button class="btn btn-sm btn-outline" onclick="window.notificationManager.handleAction('${notificationId}', '${action.id}')">
                ${action.label}
            </button>
        `).join(' ');

        return `<div class="notification-actions" style="margin-top: 0.5rem; display: flex; gap: 0.5rem;">${buttonsHtml}</div>`;
    }

    /**
     * Handle action button clicks
     */
    handleAction(notificationId, actionId) {
        const notification = this.notifications.find(n => n.id == notificationId);
        if (notification && notification.actions) {
            const action = notification.actions.find(a => a.id === actionId);
            if (action && action.callback) {
                action.callback();
            }
        }
        this.remove(notificationId);
    }

    /**
     * Update progress bar
     */
    updateProgress(notification, duration) {
        const progressBar = notification.element.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transitionDuration = duration + 'ms';

            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }
    }

    /**
     * Remove notification
     */
    remove(id) {
        const notification = this.notifications.find(n => n.id == id);
        if (!notification) return;

        // Clear timeout
        if (notification.timeout) {
            clearTimeout(notification.timeout);
        }

        // Hide animation
        notification.element.classList.add('hide');

        // Remove from DOM after animation
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
        }, 300);

        // Remove from array
        this.notifications = this.notifications.filter(n => n.id !== id);
    }

    /**
     * Remove all notifications
     */
    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification.id);
        });
    }

    /**
     * Convenience methods for different notification types
     */
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', { duration: 8000, ...options });
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    /**
     * Show API error notification
     */
    apiError(error, context = '') {
        let message = 'An error occurred';
        let title = 'API Error';

        if (error.message) {
            message = error.message;
        }

        if (context) {
            title = `${context} Error`;
        }

        if (error.statusCode) {
            title += ` (${error.statusCode})`;
        }

        return this.error(message, { title });
    }

    /**
     * Show API success notification
     */
    apiSuccess(message, context = '') {
        const title = context ? `${context} Success` : 'Success';
        return this.success(message, { title });
    }

    /**
     * Show loading notification
     */
    loading(message = 'Loading...', options = {}) {
        return this.show(message, 'info', {
            persistent: true,
            title: 'Loading',
            ...options
        });
    }

    /**
     * Show confirmation dialog
     */
    confirm(message, options = {}) {
        const {
            title = 'Confirm',
            confirmText = 'Confirm',
            cancelText = 'Cancel',
            onConfirm = () => {},
            onCancel = () => {}
        } = options;

        const actions = [
            {
                id: 'cancel',
                label: cancelText,
                callback: onCancel
            },
            {
                id: 'confirm',
                label: confirmText,
                callback: onConfirm
            }
        ];

        return this.show(message, 'warning', {
            title,
            persistent: true,
            actions
        });
    }

    /**
     * Show network status notification
     */
    networkStatus(online) {
        if (online) {
            this.success('Connection restored', {
                title: 'Online',
                duration: 3000
            });
        } else {
            this.error('Connection lost', {
                title: 'Offline',
                persistent: true
            });
        }
    }

    /**
     * Show authentication status notification
     */
    authStatus(event, data) {
        switch (event) {
            case 'login_success':
                this.success(`Welcome back, ${data.user.email}!`, {
                    title: 'Login Successful'
                });
                break;
            case 'logout':
                this.info('You have been logged out', {
                    title: 'Logged Out'
                });
                break;
            case 'token_refresh_failed':
                this.warning('Your session has expired. Please log in again.', {
                    title: 'Session Expired',
                    duration: 8000
                });
                break;
            case 'mfa_required':
                this.info('Please enter your MFA code to continue', {
                    title: 'MFA Required'
                });
                break;
        }
    }
}

// Create global notification manager instance
window.notificationManager = new NotificationManager();

// Listen for auth events
window.addEventListener('auth:login_success', (e) => {
    window.notificationManager.authStatus('login_success', e.detail);
});

window.addEventListener('auth:logout', () => {
    window.notificationManager.authStatus('logout');
});

window.addEventListener('auth:token_refresh_failed', () => {
    window.notificationManager.authStatus('token_refresh_failed');
});

window.addEventListener('auth:mfa_required', (e) => {
    window.notificationManager.authStatus('mfa_required', e.detail);
});

// Listen for network status changes
window.addEventListener('online', () => {
    window.notificationManager.networkStatus(true);
});

window.addEventListener('offline', () => {
    window.notificationManager.networkStatus(false);
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationManager;
}