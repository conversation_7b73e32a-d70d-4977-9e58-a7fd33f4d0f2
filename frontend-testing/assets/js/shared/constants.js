/**
 * Constants - API endpoints, configuration, and shared values
 */

// API Configuration
const API_CONFIG = {
    BASE_URL: 'http://localhost:3000/api/v1',
    HEALTH_URL: 'http://localhost:3000/health',
    DOCS_URL: 'http://localhost:3000/api-docs',
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 second
};

// API Endpoints
const API_ENDPOINTS = {
    // Authentication
    AUTH: {
        REGISTER: '/auth/register',
        LOGIN: '/auth/login',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        FORGOT_PASSWORD: '/auth/forgot-password',
        RESET_PASSWORD: '/auth/reset-password',
        VERIFY_EMAIL: '/auth/verify-email',
        RESEND_VERIFICATION: '/auth/resend-verification',
        PROFILE: '/auth/profile',
        CHANGE_PASSWORD: '/auth/change-password',
        CHANGE_EMAIL: '/auth/change-email',
        PERMISSIONS: '/auth/permissions',
        CHECK_PERMISSION: '/auth/check-permission',
        USERS: '/auth/users',
        USERS_RBAC: '/auth/users-rbac',
        UPDATE_USER_ROLE: '/auth/users/:userId/role',
        DELETE_USER: '/auth/users/:userId',
        CAN_ACCESS_USERS: '/auth/can-access-users',
        CAN_ACCESS_ANALYTICS: '/auth/can-access-analytics'
    },

    // Multi-Factor Authentication
    MFA: {
        INITIALIZE_SETUP: '/mfa/setup/initialize',
        COMPLETE_SETUP: '/mfa/setup/complete',
        VERIFY_TOTP: '/mfa/verify/totp',
        STATUS: '/mfa/status',
        DISABLE: '/mfa/disable',
        VERIFY_BACKUP_CODE: '/mfa/verify/backup-code',
        REGENERATE_BACKUP_CODES: '/mfa/backup-codes/regenerate'
    },

    // Session Management
    SESSIONS: {
        LIST: '/sessions',
        CURRENT: '/sessions/current',
        TERMINATE: '/sessions/terminate',
        TERMINATE_ALL_OTHERS: '/sessions/terminate-all-others',
        TRUST_DEVICE: '/sessions/trust-device',
        ACTIVITY: '/sessions/activity',
        TRUSTED_DEVICES: '/sessions/trusted-devices'
    },

    // User Management
    USERS: {
        LIST: '/users',
        GET: '/users/:id',
        UPDATE: '/users/:id',
        DELETE: '/users/:id',
        SEARCH: '/users/search',
        BY_ROLE: '/users/role/:role',
        STATS: '/users/admin/stats',
        UPDATE_ROLE: '/users/:id/role',
        BY_EMAIL: '/users/email/:email',
        PROFILE: '/users/profile'
    },

    // Security
    SECURITY: {
        STATUS: '/security/status',
        DDOS_METRICS: '/security/ddos/metrics',
        BLOCK_IP: '/security/block-ip',
        UNBLOCK_IP: '/security/unblock-ip',
        BLOCKED_IPS: '/security/blocked-ips'
    },

    // OAuth
    OAUTH: {
        INITIATE: '/auth/oauth/:provider',
        CALLBACK: '/auth/oauth/:provider/callback',
        ACCOUNTS: '/auth/oauth/accounts',
        LINK: '/auth/oauth/link/:provider',
        UNLINK: '/auth/oauth/unlink/:provider'
    },

    // Email Verification
    EMAIL_VERIFICATION: {
        REQUEST: '/email-verification/request',
        VERIFY: '/email-verification/verify/:token'
    }
};

// User Roles
const USER_ROLES = {
    USER: 'USER',
    ADMIN: 'ADMIN',
    MODERATOR: 'MODERATOR'
};

// RBAC Resources and Actions
const RBAC = {
    RESOURCES: {
        USER: 'USER',
        PROFILE: 'PROFILE',
        ANALYTICS: 'ANALYTICS',
        SECURITY: 'SECURITY',
        SYSTEM: 'SYSTEM'
    },
    ACTIONS: {
        CREATE: 'CREATE',
        READ: 'READ',
        UPDATE: 'UPDATE',
        DELETE: 'DELETE',
        MANAGE: 'MANAGE'
    }
};

// OAuth Providers
const OAUTH_PROVIDERS = {
    GOOGLE: 'google',
    FACEBOOK: 'facebook',
    GITHUB: 'github'
};

// HTTP Status Codes
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    LOCKED: 423,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503
};

// Error Messages
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    SERVER_ERROR: 'Server error. Please try again later.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    FORBIDDEN: 'Access denied.',
    NOT_FOUND: 'Resource not found.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    RATE_LIMITED: 'Too many requests. Please wait before trying again.',
    SESSION_EXPIRED: 'Your session has expired. Please log in again.',
    MFA_REQUIRED: 'Multi-factor authentication is required.',
    INVALID_CREDENTIALS: 'Invalid email or password.',
    ACCOUNT_LOCKED: 'Account is temporarily locked.',
    EMAIL_NOT_VERIFIED: 'Please verify your email address.',
    WEAK_PASSWORD: 'Password does not meet security requirements.'
};

// Success Messages
const SUCCESS_MESSAGES = {
    LOGIN_SUCCESS: 'Login successful!',
    LOGOUT_SUCCESS: 'Logged out successfully.',
    REGISTER_SUCCESS: 'Registration successful! Please check your email for verification.',
    PROFILE_UPDATED: 'Profile updated successfully.',
    PASSWORD_CHANGED: 'Password changed successfully.',
    EMAIL_VERIFIED: 'Email verified successfully.',
    MFA_ENABLED: 'Multi-factor authentication enabled.',
    MFA_DISABLED: 'Multi-factor authentication disabled.',
    SESSION_TERMINATED: 'Session terminated successfully.',
    DEVICE_TRUSTED: 'Device marked as trusted.',
    USER_UPDATED: 'User updated successfully.',
    USER_DELETED: 'User deleted successfully.'
};

// Validation Rules
const VALIDATION = {
    PASSWORD: {
        MIN_LENGTH: 8,
        REQUIRE_UPPERCASE: true,
        REQUIRE_LOWERCASE: true,
        REQUIRE_NUMBERS: true,
        REQUIRE_SPECIAL_CHARS: true,
        PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    },
    EMAIL: {
        PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    },
    USERNAME: {
        MIN_LENGTH: 3,
        MAX_LENGTH: 30,
        PATTERN: /^[a-zA-Z0-9_]+$/
    },
    NAME: {
        MAX_LENGTH: 100,
        PATTERN: /^[a-zA-Z\s'-]+$/
    }
};

// Feature Flags
const FEATURES = {
    MFA_ENABLED: true,
    OAUTH_ENABLED: true,
    EMAIL_VERIFICATION_REQUIRED: true,
    SESSION_MANAGEMENT: true,
    RATE_LIMITING: true,
    DDOS_PROTECTION: true,
    AUDIT_LOGGING: true,
    REAL_TIME_NOTIFICATIONS: true
};

// UI Configuration
const UI_CONFIG = {
    THEME: {
        DEFAULT: 'light',
        STORAGE_KEY: 'theme'
    },
    NOTIFICATIONS: {
        MAX_COUNT: 5,
        DEFAULT_DURATION: 5000,
        POSITION: 'top-right'
    },
    PAGINATION: {
        DEFAULT_LIMIT: 50,
        MAX_LIMIT: 100
    },
    REFRESH_INTERVALS: {
        SESSION_LIST: 30000, // 30 seconds
        SECURITY_STATUS: 60000, // 1 minute
        ACTIVITY_LOG: 15000 // 15 seconds
    }
};

// Storage Keys
const STORAGE_KEYS = {
    AUTH_TOKEN: 'auth_token',
    REFRESH_TOKEN: 'refresh_token',
    USER: 'user',
    THEME: 'theme',
    RECENT_ACTIVITY: 'recentActivity',
    API_REQUEST_LOG: 'apiRequestLog',
    PREFERENCES: 'preferences',
    LAST_LOGIN: 'lastLogin'
};

// Event Names
const EVENTS = {
    AUTH: {
        LOGIN_SUCCESS: 'auth:login_success',
        LOGIN_ERROR: 'auth:login_error',
        LOGOUT: 'auth:logout',
        MFA_REQUIRED: 'auth:mfa_required',
        MFA_SUCCESS: 'auth:mfa_success',
        TOKEN_REFRESHED: 'auth:token_refreshed',
        TOKEN_REFRESH_FAILED: 'auth:token_refresh_failed'
    },
    API: {
        REQUEST: 'api:request',
        RESPONSE: 'api:response',
        ERROR: 'api:error'
    },
    UI: {
        THEME_CHANGED: 'ui:theme_changed',
        NOTIFICATION_SHOWN: 'ui:notification_shown',
        MODAL_OPENED: 'ui:modal_opened',
        MODAL_CLOSED: 'ui:modal_closed'
    }
};

// Export all constants
window.API_CONFIG = API_CONFIG;
window.API_ENDPOINTS = API_ENDPOINTS;
window.USER_ROLES = USER_ROLES;
window.RBAC = RBAC;
window.OAUTH_PROVIDERS = OAUTH_PROVIDERS;
window.HTTP_STATUS = HTTP_STATUS;
window.ERROR_MESSAGES = ERROR_MESSAGES;
window.SUCCESS_MESSAGES = SUCCESS_MESSAGES;
window.VALIDATION = VALIDATION;
window.FEATURES = FEATURES;
window.UI_CONFIG = UI_CONFIG;
window.STORAGE_KEYS = STORAGE_KEYS;
window.EVENTS = EVENTS;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        API_CONFIG,
        API_ENDPOINTS,
        USER_ROLES,
        RBAC,
        OAUTH_PROVIDERS,
        HTTP_STATUS,
        ERROR_MESSAGES,
        SUCCESS_MESSAGES,
        VALIDATION,
        FEATURES,
        UI_CONFIG,
        STORAGE_KEYS,
        EVENTS
    };
}