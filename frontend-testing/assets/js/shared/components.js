/**
 * Shared Components - Reusable UI components and utilities
 */

/**
 * Modal Component
 */
class Modal {
    constructor(options = {}) {
        this.options = {
            title: 'Modal',
            content: '',
            size: 'medium', // small, medium, large
            closable: true,
            backdrop: true,
            keyboard: true,
            ...options
        };

        this.element = null;
        this.isOpen = false;
        this.onClose = options.onClose || (() => {});
        this.onOpen = options.onOpen || (() => {});

        this.create();
    }

    create() {
        this.element = document.createElement('div');
        this.element.className = 'modal-overlay';
        this.element.innerHTML = `
            <div class="modal modal-${this.options.size}">
                <div class="modal-header">
                    <h3 class="modal-title">${this.options.title}</h3>
                    ${this.options.closable ? '<button class="modal-close" type="button">&times;</button>' : ''}
                </div>
                <div class="modal-body">
                    ${this.options.content}
                </div>
                ${this.options.footer ? `<div class="modal-footer">${this.options.footer}</div>` : ''}
            </div>
        `;

        // Event listeners
        if (this.options.closable) {
            const closeBtn = this.element.querySelector('.modal-close');
            closeBtn.addEventListener('click', () => this.close());
        }

        if (this.options.backdrop) {
            this.element.addEventListener('click', (e) => {
                if (e.target === this.element) {
                    this.close();
                }
            });
        }

        if (this.options.keyboard) {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) {
                    this.close();
                }
            });
        }

        document.body.appendChild(this.element);
    }

    open() {
        if (this.isOpen) return;

        this.isOpen = true;
        this.element.classList.add('active');
        document.body.style.overflow = 'hidden';

        this.onOpen();
        window.dispatchEvent(new CustomEvent('modal:opened', { detail: this }));
    }

    close() {
        if (!this.isOpen) return;

        this.isOpen = false;
        this.element.classList.remove('active');
        document.body.style.overflow = '';

        this.onClose();
        window.dispatchEvent(new CustomEvent('modal:closed', { detail: this }));
    }

    destroy() {
        this.close();
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
    }

    setContent(content) {
        const body = this.element.querySelector('.modal-body');
        if (body) {
            body.innerHTML = content;
        }
    }

    setTitle(title) {
        const titleElement = this.element.querySelector('.modal-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }
}

/**
 * Form Validator
 */
class FormValidator {
    constructor(form, rules = {}) {
        this.form = form;
        this.rules = rules;
        this.errors = {};

        this.init();
    }

    init() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validate()) {
                e.preventDefault();
                this.showErrors();
            }
        });

        // Real-time validation
        Object.keys(this.rules).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.addEventListener('blur', () => {
                    this.validateField(fieldName);
                    this.showFieldError(fieldName);
                });

                field.addEventListener('input', () => {
                    this.clearFieldError(fieldName);
                });
            }
        });
    }

    validate() {
        this.errors = {};
        let isValid = true;

        Object.keys(this.rules).forEach(fieldName => {
            if (!this.validateField(fieldName)) {
                isValid = false;
            }
        });

        return isValid;
    }

    validateField(fieldName) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        const rules = this.rules[fieldName];
        const value = field ? field.value.trim() : '';

        if (!rules) return true;

        // Required validation
        if (rules.required && !value) {
            this.errors[fieldName] = rules.messages?.required || 'This field is required';
            return false;
        }

        // Skip other validations if field is empty and not required
        if (!value && !rules.required) {
            return true;
        }

        // Min length validation
        if (rules.minLength && value.length < rules.minLength) {
            this.errors[fieldName] = rules.messages?.minLength || `Minimum ${rules.minLength} characters required`;
            return false;
        }

        // Max length validation
        if (rules.maxLength && value.length > rules.maxLength) {
            this.errors[fieldName] = rules.messages?.maxLength || `Maximum ${rules.maxLength} characters allowed`;
            return false;
        }

        // Pattern validation
        if (rules.pattern && !rules.pattern.test(value)) {
            this.errors[fieldName] = rules.messages?.pattern || 'Invalid format';
            return false;
        }

        // Email validation
        if (rules.email && !VALIDATION.EMAIL.PATTERN.test(value)) {
            this.errors[fieldName] = rules.messages?.email || 'Invalid email address';
            return false;
        }

        // Password validation
        if (rules.password && !VALIDATION.PASSWORD.PATTERN.test(value)) {
            this.errors[fieldName] = rules.messages?.password || 'Password must contain uppercase, lowercase, number, and special character';
            return false;
        }

        // Confirm password validation
        if (rules.confirmPassword) {
            const passwordField = this.form.querySelector(`[name="${rules.confirmPassword}"]`);
            if (passwordField && value !== passwordField.value) {
                this.errors[fieldName] = rules.messages?.confirmPassword || 'Passwords do not match';
                return false;
            }
        }

        // Custom validation
        if (rules.custom && typeof rules.custom === 'function') {
            const customResult = rules.custom(value, field);
            if (customResult !== true) {
                this.errors[fieldName] = customResult || 'Invalid value';
                return false;
            }
        }

        return true;
    }

    showErrors() {
        Object.keys(this.errors).forEach(fieldName => {
            this.showFieldError(fieldName);
        });
    }

    showFieldError(fieldName) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        const error = this.errors[fieldName];

        if (!field) return;

        // Remove existing error
        this.clearFieldError(fieldName);

        if (error) {
            field.classList.add('error');

            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.textContent = error;

            field.parentNode.appendChild(errorElement);
        }
    }

    clearFieldError(fieldName) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        if (!field) return;

        field.classList.remove('error');

        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        delete this.errors[fieldName];
    }

    clearAllErrors() {
        Object.keys(this.rules).forEach(fieldName => {
            this.clearFieldError(fieldName);
        });
    }
}

/**
 * Data Table Component
 */
class DataTable {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            columns: [],
            data: [],
            pagination: true,
            pageSize: 10,
            sortable: true,
            searchable: true,
            selectable: false,
            ...options
        };

        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.searchQuery = '';
        this.selectedRows = new Set();

        this.render();
    }

    render() {
        this.container.innerHTML = `
            <div class="data-table">
                ${this.options.searchable ? this.renderSearch() : ''}
                <div class="table-container">
                    <table class="table">
                        <thead>
                            ${this.renderHeader()}
                        </thead>
                        <tbody>
                            ${this.renderBody()}
                        </tbody>
                    </table>
                </div>
                ${this.options.pagination ? this.renderPagination() : ''}
            </div>
        `;

        this.attachEvents();
    }

    renderSearch() {
        return `
            <div class="table-search">
                <input type="text" class="form-input" placeholder="Search..." value="${this.searchQuery}">
            </div>
        `;
    }

    renderHeader() {
        return `
            <tr>
                ${this.options.selectable ? '<th><input type="checkbox" class="select-all"></th>' : ''}
                ${this.options.columns.map(col => `
                    <th class="${this.options.sortable && col.sortable !== false ? 'sortable' : ''}" data-column="${col.key}">
                        ${col.title}
                        ${this.sortColumn === col.key ? (this.sortDirection === 'asc' ? ' ↑' : ' ↓') : ''}
                    </th>
                `).join('')}
            </tr>
        `;
    }

    renderBody() {
        const filteredData = this.getFilteredData();
        const paginatedData = this.options.pagination ?
            this.getPaginatedData(filteredData) : filteredData;

        if (paginatedData.length === 0) {
            return `<tr><td colspan="${this.options.columns.length + (this.options.selectable ? 1 : 0)}" class="text-center">No data available</td></tr>`;
        }

        return paginatedData.map((row, index) => `
            <tr data-index="${index}" class="${this.selectedRows.has(row.id) ? 'selected' : ''}">
                ${this.options.selectable ? `<td><input type="checkbox" class="row-select" value="${row.id}" ${this.selectedRows.has(row.id) ? 'checked' : ''}></td>` : ''}
                ${this.options.columns.map(col => `
                    <td>
                        ${col.render ? col.render(row[col.key], row) : this.formatCellValue(row[col.key], col)}
                    </td>
                `).join('')}
            </tr>
        `).join('');
    }

    renderPagination() {
        const filteredData = this.getFilteredData();
        const totalPages = Math.ceil(filteredData.length / this.options.pageSize);

        if (totalPages <= 1) return '';

        return `
            <div class="table-pagination">
                <button class="btn btn-sm" ${this.currentPage === 1 ? 'disabled' : ''} data-action="prev">Previous</button>
                <span class="pagination-info">Page ${this.currentPage} of ${totalPages}</span>
                <button class="btn btn-sm" ${this.currentPage === totalPages ? 'disabled' : ''} data-action="next">Next</button>
            </div>
        `;
    }

    formatCellValue(value, column) {
        if (value === null || value === undefined) return '';

        switch (column.type) {
            case 'date':
                return new Date(value).toLocaleDateString();
            case 'datetime':
                return new Date(value).toLocaleString();
            case 'currency':
                return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
            case 'number':
                return new Intl.NumberFormat().format(value);
            case 'boolean':
                return value ? 'Yes' : 'No';
            default:
                return String(value);
        }
    }

    getFilteredData() {
        if (!this.searchQuery) return this.options.data;

        return this.options.data.filter(row => {
            return this.options.columns.some(col => {
                const value = row[col.key];
                if (value === null || value === undefined) return false;
                return String(value).toLowerCase().includes(this.searchQuery.toLowerCase());
            });
        });
    }

    getPaginatedData(data) {
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        return data.slice(startIndex, endIndex);
    }

    attachEvents() {
        // Search
        const searchInput = this.container.querySelector('.table-search input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.currentPage = 1;
                this.render();
            });
        }

        // Sorting
        if (this.options.sortable) {
            this.container.querySelectorAll('th.sortable').forEach(th => {
                th.addEventListener('click', () => {
                    const column = th.dataset.column;
                    if (this.sortColumn === column) {
                        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        this.sortColumn = column;
                        this.sortDirection = 'asc';
                    }
                    this.sortData();
                    this.render();
                });
            });
        }

        // Pagination
        this.container.querySelectorAll('[data-action]').forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                if (action === 'prev' && this.currentPage > 1) {
                    this.currentPage--;
                } else if (action === 'next') {
                    const totalPages = Math.ceil(this.getFilteredData().length / this.options.pageSize);
                    if (this.currentPage < totalPages) {
                        this.currentPage++;
                    }
                }
                this.render();
            });
        });

        // Selection
        if (this.options.selectable) {
            const selectAll = this.container.querySelector('.select-all');
            if (selectAll) {
                selectAll.addEventListener('change', (e) => {
                    const checkboxes = this.container.querySelectorAll('.row-select');
                    checkboxes.forEach(cb => {
                        cb.checked = e.target.checked;
                        if (e.target.checked) {
                            this.selectedRows.add(cb.value);
                        } else {
                            this.selectedRows.delete(cb.value);
                        }
                    });
                });
            }

            this.container.querySelectorAll('.row-select').forEach(cb => {
                cb.addEventListener('change', (e) => {
                    if (e.target.checked) {
                        this.selectedRows.add(e.target.value);
                    } else {
                        this.selectedRows.delete(e.target.value);
                    }
                });
            });
        }
    }

    sortData() {
        if (!this.sortColumn) return;

        this.options.data.sort((a, b) => {
            const aVal = a[this.sortColumn];
            const bVal = b[this.sortColumn];

            if (aVal === null || aVal === undefined) return 1;
            if (bVal === null || bVal === undefined) return -1;

            let comparison = 0;
            if (aVal > bVal) comparison = 1;
            if (aVal < bVal) comparison = -1;

            return this.sortDirection === 'desc' ? -comparison : comparison;
        });
    }

    updateData(newData) {
        this.options.data = newData;
        this.currentPage = 1;
        this.selectedRows.clear();
        this.render();
    }

    getSelectedRows() {
        return Array.from(this.selectedRows);
    }
}

/**
 * Utility Functions
 */
const Utils = {
    /**
     * Format JSON for display
     */
    formatJSON(obj, indent = 2) {
        return JSON.stringify(obj, null, indent);
    },

    /**
     * Syntax highlight JSON
     */
    highlightJSON(json) {
        if (typeof json !== 'string') {
            json = JSON.stringify(json, null, 2);
        }

        return json
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
                let cls = 'json-number';
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'json-key';
                    } else {
                        cls = 'json-string';
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'json-boolean';
                } else if (/null/.test(match)) {
                    cls = 'json-null';
                }
                return '<span class="' + cls + '">' + match + '</span>';
            });
    },

    /**
     * Copy text to clipboard
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            window.notificationManager.success('Copied to clipboard');
            return true;
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            window.notificationManager.error('Failed to copy to clipboard');
            return false;
        }
    },

    /**
     * Download data as file
     */
    downloadFile(data, filename, type = 'application/json') {
        const blob = new Blob([data], { type });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    },

    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Throttle function
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * Generate random ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * Format file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * Format duration
     */
    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
        return `${(ms / 3600000).toFixed(1)}h`;
    },

    /**
     * Validate email
     */
    isValidEmail(email) {
        return VALIDATION.EMAIL.PATTERN.test(email);
    },

    /**
     * Validate password
     */
    isValidPassword(password) {
        return password.length >= VALIDATION.PASSWORD.MIN_LENGTH &&
               VALIDATION.PASSWORD.PATTERN.test(password);
    },

    /**
     * Get password strength
     */
    getPasswordStrength(password) {
        let score = 0;
        if (password.length >= 8) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/\d/.test(password)) score++;
        if (/[@$!%*?&]/.test(password)) score++;

        const levels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        return {
            score,
            level: levels[score] || 'Very Weak',
            percentage: (score / 5) * 100
        };
    },

    /**
     * Escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * Parse query string
     */
    parseQueryString(queryString = window.location.search) {
        const params = new URLSearchParams(queryString);
        const result = {};
        for (const [key, value] of params) {
            result[key] = value;
        }
        return result;
    },

    /**
     * Build query string
     */
    buildQueryString(params) {
        const searchParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
            if (value !== null && value !== undefined && value !== '') {
                searchParams.append(key, value);
            }
        });
        return searchParams.toString();
    }
};

// Export components and utilities
window.Modal = Modal;
window.FormValidator = FormValidator;
window.DataTable = DataTable;
window.Utils = Utils;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        Modal,
        FormValidator,
        DataTable,
        Utils
    };
}
}