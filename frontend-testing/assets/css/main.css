/* Main CSS - Core Styles for Secure Backend Testing Interface */

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;

    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;

    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Status Colors */
    --status-online: #10b981;
    --status-offline: #ef4444;
    --status-warning: #f59e0b;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;

    --border-color: #334155;
    --border-hover: #475569;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.logo .subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.server-status, .auth-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--bg-secondary);
    font-size: 0.875rem;
}

.status-indicator {
    font-size: 0.75rem;
}

.status-online {
    color: var(--status-online);
}

.status-offline {
    color: var(--status-offline);
}

.auth-status.authenticated {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-hover);
}

/* Main Content */
.main {
    min-height: calc(100vh - 140px);
    padding: 2rem 0;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
}

.welcome-section h2 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.welcome-section p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto 2rem;
}

.quick-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Features Grid */
.features-grid {
    margin-bottom: 3rem;
}

.features-grid h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Feature Cards */
.feature-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.card-header i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.card-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.feature-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.stat i {
    font-size: 0.75rem;
}

.card-actions {
    display: flex;
    justify-content: flex-end;
}

/* Recent Activity */
.recent-activity {
    margin-bottom: 2rem;
}

.recent-activity h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.activity-log {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item i {
    font-size: 1rem;
    color: var(--info-color);
}

.activity-item.activity-success i {
    color: var(--success-color);
}

.activity-item.activity-error i {
    color: var(--error-color);
}

.activity-item.activity-warning i {
    color: var(--warning-color);
}

.activity-text {
    flex: 1;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.75rem;
}

/* Footer */
.footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-links a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .welcome-section h2 {
        font-size: 1.875rem;
    }

    .grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }
}