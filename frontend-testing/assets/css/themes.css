/* Themes CSS - Theme-specific styles and animations */

/* Theme Transition Animations */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Dark Theme Specific Overrides */
[data-theme="dark"] {
    /* Scrollbar Styling for Dark Theme */
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* Light Theme Scrollbar */
[data-theme="light"] {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

[data-theme="light"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="light"] ::-webkit-scrollbar-track {
    background: #f1f5f9;
}

[data-theme="light"] ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Theme-specific Focus Styles */
[data-theme="dark"] .form-input:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

[data-theme="light"] .form-input:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Theme Toggle Animation */
.theme-toggle {
    position: relative;
    overflow: hidden;
}

.theme-toggle i {
    transition: transform 0.3s ease;
}

.theme-toggle:hover i {
    transform: rotate(15deg);
}

/* Code Syntax Highlighting for JSON Responses */
.json-viewer {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
}

.json-key {
    color: #0969da;
}

[data-theme="dark"] .json-key {
    color: #79c0ff;
}

.json-string {
    color: #032f62;
}

[data-theme="dark"] .json-string {
    color: #a5d6ff;
}

.json-number {
    color: #0550ae;
}

[data-theme="dark"] .json-number {
    color: #79c0ff;
}

.json-boolean {
    color: #8250df;
}

[data-theme="dark"] .json-boolean {
    color: #d2a8ff;
}

.json-null {
    color: #656d76;
}

[data-theme="dark"] .json-null {
    color: #8b949e;
}

/* Status Indicators with Pulse Animation */
.status-indicator.status-online {
    animation: pulse-green 2s infinite;
}

.status-indicator.status-offline {
    animation: pulse-red 2s infinite;
}

@keyframes pulse-green {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

@keyframes pulse-red {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        opacity: 1;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

[data-theme="dark"] .loading::after {
    background-color: rgba(15, 23, 42, 0.8);
}

/* Hover Effects for Interactive Elements */
.feature-card {
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

/* Success/Error State Animations */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.error-shake {
    animation: shake 0.5s ease-in-out;
}

.success-bounce {
    animation: bounce 0.5s ease-in-out;
}

/* Gradient Backgrounds for Special Elements */
.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
}

.gradient-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.gradient-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
}

.gradient-error {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .theme-toggle,
    .quick-actions {
        display: none;
    }

    .main {
        padding: 0;
    }

    .feature-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }

    body {
        background: white;
        color: black;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .feature-card:hover {
        transform: none;
    }

    .btn:hover {
        transform: none;
    }
}