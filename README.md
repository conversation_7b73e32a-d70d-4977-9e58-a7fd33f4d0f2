# 🔐 Secure Backend API

A **production-ready Express.js backend** built with TypeScript, featuring enterprise-grade security, comprehensive authentication systems, and advanced threat protection. This backend provides a complete foundation for building secure, scalable APIs with cutting-edge security features including MFA, adaptive rate limiting, DDoS protection, and comprehensive session management.

## ✨ Core Features

### 🔑 **Authentication & Authorization**
*   **JWT Authentication**: Secure token-based authentication with automatic refresh and blacklisting
*   **Multi-Factor Authentication (MFA)**: TOTP-based 2FA with QR codes, backup codes, and encrypted secret storage
*   **Enhanced Session Management**: Device fingerprinting, risk assessment, and remote session control
*   **OAuth 2.0 Integration**: Seamless social login with Google, Facebook, and GitHub
*   **Role-Based Access Control (RBAC)**: Granular permissions and role management
*   **Password Security**: Secure reset flow with rate limiting and user enumeration protection

### 🛡️ **Advanced Security**
*   **Adaptive Rate Limiting**: AI-powered rate limiting based on user behavior and trust scores
*   **DDoS Protection**: Multi-layer attack detection with automatic IP blocking and threat analysis
*   **Security Monitoring**: Real-time threat detection, alerting, and comprehensive analytics
*   **Device Trust Management**: Intelligent device recognition and trust scoring
*   **Suspicious Activity Detection**: Pattern recognition for bot detection and attack prevention
*   **Comprehensive Audit Trails**: Complete security event logging for compliance and forensics

### 🏗️ **Architecture & Development**
*   **TypeScript**: Full type safety with comprehensive interfaces and strict typing
*   **Layered Architecture**: Clean separation of concerns with controllers, services, and data layers
*   **Prisma ORM**: Type-safe database operations with automatic migrations
*   **Request Validation**: DTO-based validation using class-validator for data integrity
*   **Error Handling**: Centralized error management with consistent API responses
*   **Comprehensive Logging**: Structured logging with Winston for monitoring and debugging

### 📊 **Monitoring & Operations**
*   **Health Checks**: System health monitoring with Redis, database, and service status
*   **Performance Metrics**: Detailed analytics for rate limiting, session management, and security events
*   **Interactive API Documentation**: Complete Swagger/OpenAPI documentation with examples
*   **Production Ready**: PM2 support, clustering, and enterprise deployment configurations
*   **Testing Suite**: Comprehensive test coverage with Jest for reliability and quality assurance

## 🏗️ System Architecture

This backend implements a **modern layered architecture** with enterprise-grade security integration:

### Core Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Security Layer                          │
│     (Rate Limiting, DDoS Protection, Authentication)          │
├─────────────────────────────────────────────────────────────────┤
│                        Routes Layer                            │
│              (API Endpoints & Request Routing)                │
├─────────────────────────────────────────────────────────────────┤
│                      Controllers Layer                         │
│            (Request Validation & Response Handling)           │
├─────────────────────────────────────────────────────────────────┤
│                       Services Layer                           │
│        (Business Logic, Security Services, Integrations)      │
├─────────────────────────────────────────────────────────────────┤
│                    Data Access Layer                           │
│              (Database Operations & Caching)                  │
├─────────────────────────────────────────────────────────────────┤
│                    Infrastructure Layer                        │
│           (PostgreSQL, Redis, External Services)              │
└─────────────────────────────────────────────────────────────────┘
```

### Layer Responsibilities

#### 🛡️ **Security Layer** (`src/middlewares/`, `src/services/`)
- **Advanced Rate Limiting**: Adaptive algorithms with user trust scoring
- **DDoS Protection**: Multi-layer attack detection and automatic mitigation
- **Authentication**: JWT validation, MFA verification, session management
- **Authorization**: RBAC enforcement and permission checking
- **Threat Detection**: Real-time pattern analysis and suspicious activity monitoring

#### 🌐 **Routes Layer** (`src/routes/`)
- **API Endpoints**: RESTful API design with comprehensive endpoint coverage
- **Security Integration**: Middleware application and protection level configuration
- **Documentation**: Swagger/OpenAPI integration with detailed examples
- **Validation**: Request/response schema validation and error handling

#### 🎮 **Controllers Layer** (`src/controllers/`)
- **Request Processing**: HTTP request handling with comprehensive validation
- **Response Formatting**: Consistent API response structure and error handling
- **Security Context**: User authentication state and permission validation
- **Audit Logging**: Security event logging and compliance tracking

#### ⚙️ **Services Layer** (`src/services/`)
- **Authentication Services**: Login, registration, MFA, and session management
- **Security Services**: Rate limiting, DDoS monitoring, and threat analysis
- **Business Logic**: Core application functionality and workflow orchestration
- **Integration Services**: External API integration and third-party services

#### 💾 **Data Access Layer** (`src/prisma/`)
- **Database Operations**: Type-safe CRUD operations with Prisma ORM
- **Caching Strategy**: Redis-based caching for performance optimization
- **Data Modeling**: Comprehensive schema with security and audit considerations
- **Migration Management**: Database versioning and deployment automation

### Supporting Infrastructure

#### 🔧 **Core Components**
- **DTOs** (`src/dtos/`): Request/response validation with class-validator
- **Interfaces** (`src/interfaces/`): TypeScript type definitions and contracts
- **Utils** (`src/utils/`): Security utilities, encryption, and helper functions
- **Config** (`src/config/`): Environment-based configuration management

#### 📊 **Monitoring & Analytics**
- **Logging**: Structured logging with Winston and security event tracking
- **Metrics**: Performance monitoring and security analytics
- **Health Checks**: System health monitoring and dependency status
- **Alerting**: Real-time security alerts and notification systems

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.x or higher
- **PostgreSQL** 13.x or higher
- **Redis** 6.x or higher (for rate limiting and caching)
- **npm** or **yarn** package manager

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/secure-backend.git
   cd secure-backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env.development.local
   ```

   **Required Environment Variables:**
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/secure_backend"

   # Redis (for rate limiting and caching)
   REDIS_URL="redis://localhost:6379"

   # JWT Configuration
   JWT_SECRET="your-super-secure-jwt-secret-key"
   JWT_EXPIRES_IN="15m"
   JWT_REFRESH_EXPIRES_IN="30d"

   # MFA Encryption
   MFA_ENCRYPTION_KEY="your-32-character-encryption-key"

   # Email Configuration (for verification and notifications)
   MAILJET_API_KEY="your-mailjet-api-key"
   MAILJET_API_SECRET="your-mailjet-secret"
   MAIL_FROM="<EMAIL>"
   MAIL_FROM_NAME="Your App Name"

   # OAuth Configuration (optional)
   OAUTH_GOOGLE_CLIENT_ID="your-google-client-id"
   OAUTH_GOOGLE_CLIENT_SECRET="your-google-client-secret"
   ```

4. **Set up the database:**
   ```bash
   # Generate Prisma client
   npm run db:generate

   # Run database migrations
   npm run db:migrate:dev

   # Seed the database (optional)
   npm run db:seed
   ```

5. **Start Redis server:**
   ```bash
   # On macOS with Homebrew
   brew services start redis

   # On Ubuntu/Debian
   sudo systemctl start redis-server

   # Using Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

6. **Start the development server:**
   ```bash
   npm run dev
   ```

The server will be running at `http://localhost:3000` with interactive API documentation at `http://localhost:3000/api-docs`.

## 📚 API Documentation & Usage

### Interactive Documentation
Access the complete API documentation at `http://localhost:3000/api-docs` with:
- **Live Testing**: Test all endpoints directly from the browser
- **Request/Response Examples**: Complete examples for every endpoint
- **Authentication Flow**: Step-by-step authentication guides
- **Error Handling**: Comprehensive error response documentation

### Authentication Flow

1. **Register a new user:**
   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "username": "testuser",
       "password": "SecurePassword123!"
     }'
   ```

2. **Login to get JWT token:**
   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "SecurePassword123!"
     }'
   ```

3. **Use the token for protected endpoints:**
   ```bash
   curl -X GET http://localhost:3000/api/v1/user/profile \
     -H "Authorization: Bearer <your-jwt-token>"
   ```

## 🔐 Advanced Security Features

### 🔑 Multi-Factor Authentication (MFA)

Enterprise-grade TOTP-based two-factor authentication system:

**Core Features:**
- **QR Code Setup**: Seamless integration with Google Authenticator, Authy, Microsoft Authenticator
- **Backup Codes**: 10 single-use recovery codes with usage tracking
- **Encrypted Storage**: AES-256-CBC encryption for TOTP secrets and backup codes
- **Time-Limited Setup**: Secure setup tokens with automatic expiration
- **Comprehensive Audit**: Complete logging of all MFA activities and attempts

**API Endpoints:**
```bash
# Initialize MFA setup
POST /api/v1/mfa/setup/initialize

# Complete MFA setup with TOTP verification
POST /api/v1/mfa/setup/complete
{
  "setupToken": "setup-token-from-initialize",
  "totpCode": "123456"
}

# Verify TOTP during login
POST /api/v1/mfa/verify/totp
{
  "totpCode": "123456"
}

# Use backup code for recovery
POST /api/v1/mfa/verify/backup-code
{
  "backupCode": "recovery-code-123"
}

# Get MFA status and remaining backup codes
GET /api/v1/mfa/status

# Regenerate backup codes
POST /api/v1/mfa/backup-codes/regenerate

# Disable MFA (requires TOTP verification)
POST /api/v1/mfa/disable
{
  "totpCode": "123456"
}
```

### 📱 Enhanced Session Management

Comprehensive session tracking with device intelligence and risk assessment:

**Advanced Features:**
- **Device Fingerprinting**: Unique device identification using browser characteristics
- **Risk-Based Authentication**: Dynamic risk scoring based on login patterns and behavior
- **Session Limits**: Configurable maximum active sessions per user (default: 10)
- **Activity Tracking**: Detailed audit trail of all session activities and events
- **Remote Session Control**: Terminate sessions from any device with real-time updates
- **Device Trust Management**: User-controlled device trust levels with automatic learning

**API Endpoints:**
```bash
# List all user sessions with detailed information
GET /api/v1/sessions

# Get current session details
GET /api/v1/sessions/current

# Terminate a specific session
POST /api/v1/sessions/terminate
{
  "sessionId": "session-uuid"
}

# Keep current session, terminate all others
POST /api/v1/sessions/terminate-all-others

# Logout current session
POST /api/v1/sessions/logout

# Mark current device as trusted
POST /api/v1/sessions/trust-device

# Get session activity history
GET /api/v1/sessions/activity?limit=50&offset=0

# List trusted devices
GET /api/v1/sessions/trusted-devices
```

### 🛡️ Advanced Rate Limiting & DDoS Protection

AI-powered adaptive rate limiting with sophisticated threat detection:

**Intelligent Features:**
- **Adaptive Algorithms**: Rate limits adjust based on user trust scores and behavior patterns
- **Multi-Layer DDoS Protection**: Request pattern analysis, IP reputation, and behavioral detection
- **Threat Intelligence**: Real-time pattern recognition for bot detection and attack prevention
- **Automatic IP Blocking**: Dynamic blocking with graduated response based on threat level
- **Trust Score System**: Machine learning-based user trust scoring with continuous adaptation

**Protection Levels:**
- **CRITICAL**: 5-15 requests/minute (sensitive operations like MFA, password changes)
- **HIGH**: 20-50 requests/minute (authentication endpoints)
- **MEDIUM**: 50-100 requests/minute (public APIs)
- **LOW**: 100-200 requests/minute (general endpoints)

**Security Management API:**
```bash
# Get overall security system status
GET /api/v1/security/status

# Get DDoS attack metrics and statistics
GET /api/v1/security/ddos/metrics?hours=24

# Get rate limiting statistics
GET /api/v1/security/rate-limit/stats?hours=24

# Get security alerts
GET /api/v1/security/alerts?limit=50&severity=HIGH

# Check IP blocking status
GET /api/v1/security/ip/***********/status

# Manually block an IP address
POST /api/v1/security/ip/block
{
  "ip": "***********",
  "reason": "Suspicious activity detected",
  "duration": 3600
}

# Unblock an IP address
DELETE /api/v1/security/ip/***********/unblock

# Get user trust score
GET /api/v1/security/user/trust-score
```

## 📊 Database Schema & Models

### 👤 User Management Models

**User Model:**
```typescript
model User {
  id                    String    @id @default(cuid())
  email                 String?   @unique
  username              String?   @unique
  name                  String?
  passwordHash          String?
  role                  Role      @default(USER)
  emailVerified         DateTime?
  failedLoginAttempts   Int       @default(0)
  lockoutExpires        DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  sessions              UserSession[]
  trustedDevices        TrustedDevice[]
  mfaSetup              MFASetup?
  sessionActivities     SessionActivity[]
}
```

### 🔐 Security & Authentication Models

**Enhanced Session Management:**
```typescript
model UserSession {
  id                    String    @id @default(cuid())
  userId                String
  sessionToken          String    @unique
  deviceId              String
  deviceName            String?
  deviceType            String?
  userAgent             String?
  ipAddress             String?
  location              String?
  isActive              Boolean   @default(true)
  isTrusted             Boolean   @default(false)
  lastActivity          DateTime  @default(now())
  createdAt             DateTime  @default(now())
  expiresAt             DateTime
  terminatedAt          DateTime?
  terminatedBy          String?
  terminationReason     String?
  loginAttempts         Int       @default(0)
  suspiciousActivity    Boolean   @default(false)
  riskScore             Float     @default(0.0)

  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  activities            SessionActivity[]
}

model TrustedDevice {
  id                    String    @id @default(cuid())
  userId                String
  deviceId              String
  deviceName            String?
  deviceType            String?
  fingerprint           String
  userAgent             String?
  firstSeen             DateTime  @default(now())
  lastSeen              DateTime  @default(now())
  trustLevel            String    @default("unknown")
  trustedAt             DateTime?
  trustedBy             String?
  isActive              Boolean   @default(true)

  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model SessionActivity {
  id                    String    @id @default(cuid())
  sessionId             String
  userId                String
  action                String
  details               Json?
  ipAddress             String?
  userAgent             String?
  timestamp             DateTime  @default(now())
  riskScore             Float     @default(0.0)

  session               UserSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

**Multi-Factor Authentication:**
```typescript
model MFASetup {
  id                    String    @id @default(cuid())
  userId                String    @unique
  secret                String    // Encrypted TOTP secret
  isEnabled             Boolean   @default(false)
  backupCodes           String    // Encrypted backup codes JSON
  setupToken            String?
  setupTokenExpires     DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  backupCodeUsage       MFABackupCode[]
}

model MFABackupCode {
  id                    String    @id @default(cuid())
  mfaSetupId            String
  codeHash              String
  isUsed                Boolean   @default(false)
  usedAt                DateTime?
  createdAt             DateTime  @default(now())

  mfaSetup              MFASetup  @relation(fields: [mfaSetupId], references: [id], onDelete: Cascade)
}
```

### 🛡️ Security & Audit Models

**Comprehensive Audit Trail:**
```typescript
model AuditLog {
  id                    String    @id @default(cuid())
  userId                String?
  action                String
  resource              String?
  resourceId            String?
  details               Json?
  ipAddress             String?
  userAgent             String?
  timestamp             DateTime  @default(now())
  severity              String    @default("INFO")
  category              String?
}
```

**Password Reset & Email Verification:**
```typescript
model PasswordResetToken {
  id                    String    @id @default(cuid())
  userId                String
  token                 String    @unique
  expiresAt             DateTime
  isUsed                Boolean   @default(false)
  usedAt                DateTime?
  createdAt             DateTime  @default(now())
  ipAddress             String?
  userAgent             String?
}

model EmailVerification {
  id                    String    @id @default(cuid())
  userId                String
  email                 String
  token                 String    @unique
  expiresAt             DateTime
  isVerified            Boolean   @default(false)
  verifiedAt            DateTime?
  createdAt             DateTime  @default(now())
  attempts              Int       @default(0)
}
```

## 🛡️ Enterprise Security Implementation

### 🔒 Authentication & Authorization Security

**JWT Token Management:**
- **Secure Token Generation**: Cryptographically secure random token generation
- **Token Rotation**: Automatic refresh token rotation with blacklisting
- **Session Binding**: Tokens bound to specific sessions and devices
- **Expiration Management**: Configurable token lifetimes with automatic cleanup

**Password Security:**
- **bcrypt Hashing**: Industry-standard password hashing with configurable salt rounds
- **Password Policies**: Configurable complexity requirements and validation
- **Account Lockout**: Progressive lockout after failed attempts with exponential backoff
- **Secure Reset Flow**: Time-limited tokens with rate limiting and audit trails

### 🔐 Multi-Factor Authentication Security

**TOTP Implementation:**
- **RFC 6238 Compliance**: Standard TOTP implementation with time tolerance
- **Secure Secret Generation**: Cryptographically secure secret generation
- **AES-256-CBC Encryption**: Military-grade encryption for secret storage
- **Backup Code Security**: Single-use codes with secure hashing and usage tracking

**Setup Security:**
- **Time-Limited Setup**: Setup tokens with automatic expiration
- **Verification Requirements**: Multi-step verification process
- **Audit Logging**: Complete audit trail of all MFA activities

### 📱 Session Security Architecture

**Device Intelligence:**
- **Fingerprinting**: Multi-factor device identification using browser characteristics
- **Trust Scoring**: Machine learning-based trust assessment
- **Behavioral Analysis**: Pattern recognition for legitimate vs. suspicious activity
- **Risk Assessment**: Real-time risk scoring based on multiple factors

**Session Management:**
- **Concurrent Session Limits**: Configurable maximum active sessions
- **Activity Tracking**: Comprehensive logging of all session events
- **Remote Termination**: Real-time session termination across devices
- **Automatic Cleanup**: Intelligent cleanup of expired and inactive sessions

### 🛡️ Advanced Threat Protection

**Rate Limiting & DDoS Protection:**
- **Adaptive Algorithms**: AI-powered rate limiting based on user behavior
- **Multi-Layer Detection**: Request pattern analysis, timing analysis, and threat intelligence
- **Automatic Mitigation**: Dynamic IP blocking with graduated response
- **Threat Intelligence**: Real-time pattern recognition and attack classification

**Data Protection:**
- **Input Validation**: Comprehensive validation using class-validator
- **SQL Injection Prevention**: Parameterized queries and ORM protection
- **XSS Protection**: Content Security Policy and output encoding
- **CORS Configuration**: Strict cross-origin resource sharing policies

### 📊 Monitoring & Compliance

**Comprehensive Audit Trails:**
- **Security Events**: Complete logging of authentication, authorization, and security events
- **User Activities**: Detailed tracking of user actions and system interactions
- **System Events**: Infrastructure and application-level event logging
- **Compliance Reporting**: Automated compliance reporting and data retention

**Real-Time Monitoring:**
- **Threat Detection**: Real-time analysis of security events and patterns
- **Performance Monitoring**: System performance and security metrics
- **Alert Management**: Multi-level alerting with severity classification
- **Dashboard Analytics**: Real-time security dashboards and visualizations

### 🔧 Security Configuration

**Environment-Based Security:**
```env
# JWT Security
JWT_SECRET="your-256-bit-secret-key"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="30d"

# MFA Security
MFA_ENCRYPTION_KEY="your-32-character-encryption-key"

# Rate Limiting
REDIS_URL="redis://localhost:6379"

# Password Security
BCRYPT_SALT_ROUNDS=12

# Session Security
SESSION_MAX_AGE=86400000
SESSION_CLEANUP_INTERVAL=3600000
```

**Security Headers:**
- **Helmet.js**: Comprehensive security headers
- **HSTS**: HTTP Strict Transport Security
- **CSP**: Content Security Policy
- **X-Frame-Options**: Clickjacking protection
- **X-Content-Type-Options**: MIME type sniffing protection

## 🚀 Production Deployment

### Docker Deployment

**Dockerfile:**
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

**Docker Compose:**
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/secure_backend
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: secure_backend
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Environment Configuration

**Production Environment Variables:**
```env
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://user:pass@localhost:5432/secure_backend_prod
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-production-jwt-secret-256-bits
MFA_ENCRYPTION_KEY=your-production-mfa-key-32-chars
BCRYPT_SALT_ROUNDS=12
LOG_LEVEL=info
```

### Health Checks & Monitoring

**Health Check Endpoint:**
```bash
GET /health
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "rateLimiting": "active"
  }
}
```

## 🧪 Testing & Quality Assurance

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run integration tests
npm run test:integration

# Run security tests
npm run test:security

# Run performance tests
npm run test:performance
```

### Test Coverage

The project maintains comprehensive test coverage across:
- **Unit Tests**: Service layer and utility functions
- **Integration Tests**: API endpoints and database operations
- **Security Tests**: Authentication, authorization, and rate limiting
- **Performance Tests**: Load testing and stress testing

### Code Quality

```bash
# Lint code
npm run lint

# Format code
npm run format

# Type checking
npm run type-check

# Security audit
npm audit
```

## 📖 Additional Documentation

- **[API Documentation](docs/API.md)**: Complete API endpoint documentation
- **[Frontend Integration Guide](docs/FRONTEND.md)**: Frontend development and testing guide
- **[Security Guide](docs/SECURITY.md)**: Detailed security implementation guide
- **[Deployment Guide](docs/DEPLOYMENT.md)**: Production deployment instructions
- **[Contributing Guide](CONTRIBUTING.md)**: Development and contribution guidelines

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details on:
- Development setup and workflow
- Code style and standards
- Testing requirements
- Security considerations
- Pull request process

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

---

## 🎯 Key Features Summary

✅ **Enterprise Authentication** - JWT, MFA, OAuth 2.0, RBAC
✅ **Advanced Security** - Adaptive rate limiting, DDoS protection, threat detection
✅ **Session Management** - Device fingerprinting, risk assessment, remote control
✅ **Comprehensive Monitoring** - Real-time analytics, security alerts, audit trails
✅ **Production Ready** - Docker support, health checks, performance optimization
✅ **Developer Friendly** - TypeScript, comprehensive docs, interactive API testing

**Built with security-first principles for modern applications requiring enterprise-grade protection.**
