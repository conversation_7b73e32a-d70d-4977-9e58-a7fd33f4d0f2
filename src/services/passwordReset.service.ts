import { User } from '@prisma/client';
import { UserRepository } from '../repositories/user.repository';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeForLog, sanitizeError, sanitizeAuthEvent } from '../utils/logSanitizer';
import { PasswordUtils } from '../utils/password';
import { EmailService } from './email.service';
import { AuthEvent } from '../types/template';
import { prisma } from '../loaders/prisma';
import crypto from 'crypto';

export interface PasswordResetContext {
  ip?: string;
  userAgent?: string;
  deviceFingerprint?: string;
}

export interface PasswordResetResult {
  success: boolean;
  message: string;
  canResendAfter?: Date;
}

/**
 * Rate limiting for password reset requests
 */
class PasswordResetRateLimit {
  private static readonly RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
  private static readonly MAX_ATTEMPTS = 3; // Max 3 reset requests per window
  private static readonly COOLDOWN_PERIOD = 5 * 60 * 1000; // 5 minutes between requests
  private static attempts: Map<string, { count: number; firstAttempt: number; lastAttempt: number }> = new Map();

  static canRequest(identifier: string): { canRequest: boolean; attemptsLeft: number } {
    const now = Date.now();
    const userAttempts = this.attempts.get(identifier);

    if (!userAttempts) {
      return { canRequest: true, attemptsLeft: this.MAX_ATTEMPTS };
    }

    // Reset if window has expired
    if (now - userAttempts.firstAttempt > this.RATE_LIMIT_WINDOW) {
      this.attempts.delete(identifier);
      return { canRequest: true, attemptsLeft: this.MAX_ATTEMPTS };
    }

    // Check cooldown period
    if (now - userAttempts.lastAttempt < this.COOLDOWN_PERIOD) {
      return { canRequest: false, attemptsLeft: Math.max(0, this.MAX_ATTEMPTS - userAttempts.count) };
    }

    // Check max attempts
    if (userAttempts.count >= this.MAX_ATTEMPTS) {
      return { canRequest: false, attemptsLeft: 0 };
    }

    return { canRequest: true, attemptsLeft: this.MAX_ATTEMPTS - userAttempts.count };
  }

  static recordAttempt(identifier: string): void {
    const now = Date.now();
    const userAttempts = this.attempts.get(identifier);

    if (!userAttempts) {
      this.attempts.set(identifier, { count: 1, firstAttempt: now, lastAttempt: now });
    } else {
      userAttempts.count++;
      userAttempts.lastAttempt = now;
    }
  }

  static getRemainingCooldown(identifier: string): number {
    const userAttempts = this.attempts.get(identifier);
    if (!userAttempts) return 0;

    const now = Date.now();
    const cooldownRemaining = this.COOLDOWN_PERIOD - (now - userAttempts.lastAttempt);
    return Math.max(0, Math.ceil(cooldownRemaining / 1000));
  }
}

export class PasswordResetService {
  private emailService: EmailService;

  constructor() {
    this.emailService = new EmailService();
  }

  /**
   * Generate a secure password reset token
   */
  private generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Hash the reset token for database storage
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Calculate token expiry (30 minutes from now)
   */
  private calculateTokenExpiry(): Date {
    return new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
  }

  /**
   * Log info with sanitization
   */
  private logInfo(message: string, data: any): void {
    logger.info(message, sanitizeForLog(data));
  }

  /**
   * Log warning with sanitization
   */
  private logWarn(message: string, data: any): void {
    logger.warn(message, sanitizeForLog(data));
  }

  /**
   * Log error with sanitization
   */
  private logError(message: string, data: any): void {
    logger.error(message, sanitizeForLog(data));
  }

  /**
   * Initiate password reset process
   */
  async initiatePasswordReset(
    identifier: string, // email or username
    context?: PasswordResetContext,
  ): Promise<PasswordResetResult> {
    try {
      // Check rate limiting
      const rateLimitCheck = PasswordResetRateLimit.canRequest(identifier);
      if (!rateLimitCheck.canRequest) {
        const cooldownSeconds = PasswordResetRateLimit.getRemainingCooldown(identifier);
        const canResendAfter = new Date(Date.now() + cooldownSeconds * 1000);

        this.logWarn('Password reset request rate limited', {
          identifier,
          cooldownSeconds,
          attemptsLeft: rateLimitCheck.attemptsLeft,
          ip: context?.ip,
        });

        return {
          success: false,
          message: `Please wait ${Math.ceil(cooldownSeconds / 60)} minutes before requesting another password reset.`,
          canResendAfter,
        };
      }

      // Find user by email or username
      let user: User | null = null;
      if (identifier.includes('@')) {
        user = await UserRepository.findByEmail(identifier);
      } else {
        user = await UserRepository.findByUsername(identifier);
      }

      // Record the attempt regardless of whether user exists (prevent user enumeration)
      PasswordResetRateLimit.recordAttempt(identifier);

      if (!user || !user.email) {
        // Don't reveal whether user exists - return success message
        this.logWarn('Password reset requested for non-existent user', {
          identifier,
          ip: context?.ip,
        });

        return {
          success: true,
          message: 'If an account with that email exists, you will receive a password reset link.',
        };
      }

      // Check if user has a password (OAuth-only users can't reset password)
      if (!user.passwordHash) {
        this.logWarn('Password reset requested for OAuth-only user', {
          userId: user.id,
          email: user.email,
          ip: context?.ip,
        });

        return {
          success: true,
          message: 'If an account with that email exists, you will receive a password reset link.',
        };
      }

      // Invalidate any existing password reset tokens for this user
      await prisma.passwordResetToken.updateMany({
        where: {
          userId: user.id,
          isUsed: false,
          expires: { gt: new Date() },
        },
        data: {
          isUsed: true,
          usedAt: new Date(),
        },
      });

      // Generate new reset token
      const token = this.generateResetToken();
      const tokenHash = this.hashToken(token);
      const expiresAt = this.calculateTokenExpiry();

      // Create password reset token record
      await prisma.passwordResetToken.create({
        data: {
          userId: user.id,
          token: tokenHash,
          expires: expiresAt,
          ipAddress: context?.ip,
          userAgent: context?.userAgent,
        },
      });

      // Send password reset email
      try {
        const emailResult = await this.emailService.sendPasswordResetEmail(user as any, token);

        if (!emailResult.success) {
          this.logError('Failed to send password reset email', {
            userId: user.id,
            email: user.email,
            error: emailResult.error,
            ip: context?.ip,
          });

          // In development/testing, we might not have email configured
          // Log the token for testing purposes
          if (process.env['NODE_ENV'] === 'development' || process.env['NODE_ENV'] === 'test') {
            this.logInfo('Password reset token for testing', {
              userId: user.id,
              email: user.email,
              token: token.substring(0, 8) + '...',
              fullTokenForTesting: token, // Only in dev/test
            });
          } else {
            throw new HttpException(500, 'Failed to send password reset email. Please try again later.');
          }
        }
      } catch (emailError) {
        this.logError('Email service error during password reset', {
          userId: user.id,
          email: user.email,
          error: sanitizeError(emailError),
          ip: context?.ip,
        });

        // In development/testing, continue without email
        if (process.env['NODE_ENV'] === 'development' || process.env['NODE_ENV'] === 'test') {
          this.logInfo('Password reset token for testing (email failed)', {
            userId: user.id,
            email: user.email,
            token: token.substring(0, 8) + '...',
            fullTokenForTesting: token, // Only in dev/test
          });
        } else {
          throw new HttpException(500, 'Failed to send password reset email. Please try again later.');
        }
      }

      // Log successful password reset initiation
      const authEvent: AuthEvent = {
        type: 'AUTH_PASSWORD_RESET_REQUESTED' as any,
        timestamp: new Date(),
        userId: user.id,
        email: user.email,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('Password reset initiated successfully', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'If an account with that email exists, you will receive a password reset link.',
      };
    } catch (error) {
      this.logError('Failed to initiate password reset', {
        identifier,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Reset password using token
   */
  async resetPassword(
    token: string,
    newPassword: string,
    context?: PasswordResetContext,
  ): Promise<PasswordResetResult> {
    try {
      if (!token || !newPassword) {
        throw new HttpException(400, 'Token and new password are required');
      }

      // Hash the provided token to match database
      const tokenHash = this.hashToken(token);

      // Find valid reset token
      const resetToken = await prisma.passwordResetToken.findFirst({
        where: {
          token: tokenHash,
          isUsed: false,
          expires: { gt: new Date() },
        },
        include: {
          user: true,
        },
      });

      if (!resetToken) {
        this.logWarn('Invalid or expired password reset token used', {
          tokenHash: tokenHash.substring(0, 8) + '...',
          ip: context?.ip,
        });

        throw new HttpException(400, 'Invalid or expired password reset token');
      }

      const user = resetToken.user;

      // Validate new password strength (this should be done by middleware, but double-check)
      if (newPassword.length < 8) {
        throw new HttpException(400, 'Password must be at least 8 characters long');
      }

      // Check if new password is same as current (prevent password reuse)
      if (user.passwordHash) {
        const isSamePassword = await PasswordUtils.compare(newPassword, user.passwordHash);
        if (isSamePassword) {
          throw new HttpException(400, 'New password must be different from current password');
        }
      }

      // Hash new password
      const newPasswordHash = await PasswordUtils.hash(newPassword);

      // Update user password and reset failed login attempts
      await UserRepository.update(user.id, {
        passwordHash: newPasswordHash,
        failedLoginAttempts: 0,
        lockoutExpires: null,
      });

      // Mark token as used
      await prisma.passwordResetToken.update({
        where: { id: resetToken.id },
        data: {
          isUsed: true,
          usedAt: new Date(),
        },
      });

      // Log successful password reset
      const authEvent: AuthEvent = {
        type: 'AUTH_PASSWORD_RESET_COMPLETED' as any,
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('Password reset completed successfully', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'Password has been reset successfully. You can now log in with your new password.',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logError('Failed to reset password', {
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Validate reset token (for frontend validation)
   */
  async validateResetToken(token: string): Promise<{ valid: boolean; expired?: boolean }> {
    try {
      const tokenHash = this.hashToken(token);

      const resetToken = await prisma.passwordResetToken.findFirst({
        where: {
          token: tokenHash,
          isUsed: false,
        },
      });

      if (!resetToken) {
        return { valid: false };
      }

      if (resetToken.expires < new Date()) {
        return { valid: false, expired: true };
      }

      return { valid: true };
    } catch (error) {
      this.logError('Failed to validate reset token', {
        error: sanitizeError(error),
      });
      return { valid: false };
    }
  }
}
