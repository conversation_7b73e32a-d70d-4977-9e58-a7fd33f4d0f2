import { redis } from '../config/redis';
import { logger } from '../utils/secureLogger';
import { sanitizeForLog, sanitizeError } from '../utils/logSanitizer';

export interface AttackMetrics {
  totalAttacks: number;
  activeAttacks: number;
  blockedIPs: number;
  topAttackingIPs: Array<{ ip: string; count: number }>;
  attacksByCountry: Array<{ country: string; count: number }>;
  attackPatterns: Array<{ pattern: string; count: number }>;
  timeSeriesData: Array<{ timestamp: number; attacks: number }>;
}

export interface SecurityAlert {
  id: string;
  type: 'DDOS_ATTACK' | 'RATE_LIMIT_VIOLATION' | 'SUSPICIOUS_PATTERN' | 'IP_BLOCKED';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  timestamp: Date;
  ip: string;
  userAgent?: string;
  endpoint?: string;
  description: string;
  metadata: Record<string, any>;
}

export interface RateLimitStats {
  endpoint: string;
  totalRequests: number;
  blockedRequests: number;
  averageResponseTime: number;
  peakRequestsPerMinute: number;
  uniqueIPs: number;
}

/**
 * DDoS Monitoring and Analytics Service
 * Provides comprehensive monitoring, alerting, and analytics for DDoS attacks and rate limiting
 */
export class DDoSMonitoringService {
  private static readonly METRICS_RETENTION_DAYS = 30;
  private static readonly ALERT_RETENTION_DAYS = 7;

  /**
   * Record a security event
   */
  static async recordSecurityEvent(
    type: SecurityAlert['type'],
    severity: SecurityAlert['severity'],
    ip: string,
    description: string,
    metadata: Record<string, any> = {},
  ): Promise<void> {
    try {
      const alert: SecurityAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type,
        severity,
        timestamp: new Date(),
        ip,
        userAgent: metadata['userAgent'],
        endpoint: metadata['endpoint'],
        description,
        metadata,
      };

      // Store alert in Redis
      const alertKey = `security:alerts:${alert.id}`;
      await redis.setex(alertKey, this.ALERT_RETENTION_DAYS * 24 * 60 * 60, JSON.stringify(alert));

      // Add to time-series data
      const timeSeriesKey = `security:timeseries:${type}`;
      const timestamp = Math.floor(Date.now() / 60000) * 60000; // Round to minute
      await redis.zincrby(timeSeriesKey, 1, timestamp.toString());
      await redis.expire(timeSeriesKey, this.METRICS_RETENTION_DAYS * 24 * 60 * 60);

      // Update IP-based metrics
      const ipMetricsKey = `security:ip_metrics:${ip}`;
      await redis.hincrby(ipMetricsKey, type, 1);
      await redis.hincrby(ipMetricsKey, 'total_events', 1);
      await redis.hset(ipMetricsKey, 'last_seen', Date.now().toString());
      await redis.expire(ipMetricsKey, this.METRICS_RETENTION_DAYS * 24 * 60 * 60);

      // Log the event
      logger.warn(
        'Security event recorded',
        sanitizeForLog({
          alertId: alert.id,
          type,
          severity,
          ip,
          description,
          metadata,
        }),
      );

      // Send critical alerts immediately
      if (severity === 'CRITICAL') {
        await this.sendCriticalAlert(alert);
      }
    } catch (error) {
      logger.error('Failed to record security event', {
        type,
        severity,
        ip,
        error: sanitizeError(error),
      });
    }
  }

  /**
   * Get comprehensive attack metrics
   */
  static async getAttackMetrics(timeRangeHours: number = 24): Promise<AttackMetrics> {
    try {
      const now = Date.now();
      const startTime = now - timeRangeHours * 60 * 60 * 1000;

      // Get time series data for attacks
      const timeSeriesKey = 'security:timeseries:DDOS_ATTACK';
      const timeSeriesData = await redis.zrangebyscore(timeSeriesKey, startTime, now, 'WITHSCORES');

      // Process time series data
      const processedTimeSeries = [];
      for (let i = 0; i < timeSeriesData.length; i += 2) {
        processedTimeSeries.push({
          timestamp: parseInt(timeSeriesData[i + 1] || '0'),
          attacks: parseInt(timeSeriesData[i] || '0'),
        });
      }

      // Get blocked IPs count
      const blockedIPsPattern = 'ddos:blocked:*';
      const blockedIPs = await redis.keys(blockedIPsPattern);

      // Get top attacking IPs
      const ipMetricsPattern = 'security:ip_metrics:*';
      const ipKeys = await redis.keys(ipMetricsPattern);
      const topAttackingIPs = [];

      for (const key of ipKeys.slice(0, 100)) {
        // Limit to prevent performance issues
        const ip = key.replace('security:ip_metrics:', '');
        const attackCount = await redis.hget(key, 'DDOS_ATTACK');
        if (attackCount && parseInt(attackCount) > 0) {
          topAttackingIPs.push({
            ip,
            count: parseInt(attackCount),
          });
        }
      }

      // Sort and limit top attacking IPs
      topAttackingIPs.sort((a, b) => b.count - a.count);
      const limitedTopIPs = topAttackingIPs.slice(0, 10);

      // Get attack patterns
      const attackPatterns = await this.getAttackPatterns(timeRangeHours);

      return {
        totalAttacks: processedTimeSeries.reduce((sum, data) => sum + data.attacks, 0),
        activeAttacks: await this.getActiveAttacksCount(),
        blockedIPs: blockedIPs.length,
        topAttackingIPs: limitedTopIPs,
        attacksByCountry: [], // Would require GeoIP service
        attackPatterns,
        timeSeriesData: processedTimeSeries,
      };
    } catch (error) {
      logger.error('Failed to get attack metrics', {
        timeRangeHours,
        error: sanitizeError(error),
      });

      return {
        totalAttacks: 0,
        activeAttacks: 0,
        blockedIPs: 0,
        topAttackingIPs: [],
        attacksByCountry: [],
        attackPatterns: [],
        timeSeriesData: [],
      };
    }
  }

  /**
   * Get rate limiting statistics for endpoints
   */
  static async getRateLimitStats(timeRangeHours: number = 24): Promise<RateLimitStats[]> {
    try {
      const stats: RateLimitStats[] = [];

      // Get endpoint metrics
      const endpointPattern = 'rl_stats:endpoint:*';
      const endpointKeys = await redis.keys(endpointPattern);

      for (const key of endpointKeys) {
        const endpoint = key.replace('rl_stats:endpoint:', '');
        const endpointData = await redis.hgetall(key);

        if (endpointData) {
          stats.push({
            endpoint,
            totalRequests: parseInt(endpointData['total_requests'] || '0'),
            blockedRequests: parseInt(endpointData['blocked_requests'] || '0'),
            averageResponseTime: parseFloat(endpointData['avg_response_time'] || '0'),
            peakRequestsPerMinute: parseInt(endpointData['peak_requests_per_minute'] || '0'),
            uniqueIPs: parseInt(endpointData['unique_ips'] || '0'),
          });
        }
      }

      return stats.sort((a, b) => b.totalRequests - a.totalRequests);
    } catch (error) {
      logger.error('Failed to get rate limit stats', {
        timeRangeHours,
        error: sanitizeError(error),
      });
      return [];
    }
  }

  /**
   * Get recent security alerts
   */
  static async getSecurityAlerts(limit: number = 50, severity?: SecurityAlert['severity']): Promise<SecurityAlert[]> {
    try {
      const alertPattern = 'security:alerts:*';
      const alertKeys = await redis.keys(alertPattern);
      const alerts: SecurityAlert[] = [];

      // Get alerts data
      for (const key of alertKeys) {
        const alertData = await redis.get(key);
        if (alertData) {
          const alert = JSON.parse(alertData) as SecurityAlert;
          if (!severity || alert.severity === severity) {
            alerts.push(alert);
          }
        }
      }

      // Sort by timestamp (newest first) and limit
      return alerts.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, limit);
    } catch (error) {
      logger.error('Failed to get security alerts', {
        limit,
        severity,
        error: sanitizeError(error),
      });
      return [];
    }
  }

  /**
   * Update endpoint statistics
   */
  static async updateEndpointStats(
    endpoint: string,
    ip: string,
    responseTime: number,
    blocked: boolean = false,
  ): Promise<void> {
    try {
      const statsKey = `rl_stats:endpoint:${endpoint}`;
      const now = Date.now();
      const minute = Math.floor(now / 60000) * 60000;

      // Update basic counters
      await redis.hincrby(statsKey, 'total_requests', 1);
      if (blocked) {
        await redis.hincrby(statsKey, 'blocked_requests', 1);
      }

      // Update response time (simple moving average)
      const currentAvg = parseFloat((await redis.hget(statsKey, 'avg_response_time')) || '0');
      const requestCount = parseInt((await redis.hget(statsKey, 'total_requests')) || '1');
      const newAvg = (currentAvg * (requestCount - 1) + responseTime) / requestCount;
      await redis.hset(statsKey, 'avg_response_time', newAvg.toString());

      // Track unique IPs
      const ipSetKey = `rl_stats:ips:${endpoint}`;
      await redis.sadd(ipSetKey, ip);
      const uniqueIPs = await redis.scard(ipSetKey);
      await redis.hset(statsKey, 'unique_ips', uniqueIPs.toString());

      // Track requests per minute
      const minuteKey = `rl_stats:minute:${endpoint}:${minute}`;
      const minuteCount = await redis.incr(minuteKey);
      await redis.expire(minuteKey, 3600); // 1 hour

      // Update peak requests per minute
      const currentPeak = parseInt((await redis.hget(statsKey, 'peak_requests_per_minute')) || '0');
      if (minuteCount > currentPeak) {
        await redis.hset(statsKey, 'peak_requests_per_minute', minuteCount.toString());
      }

      // Set expiration
      await redis.expire(statsKey, this.METRICS_RETENTION_DAYS * 24 * 60 * 60);
      await redis.expire(ipSetKey, this.METRICS_RETENTION_DAYS * 24 * 60 * 60);
    } catch (error) {
      logger.error('Failed to update endpoint stats', {
        endpoint,
        ip,
        responseTime,
        blocked,
        error: sanitizeError(error),
      });
    }
  }

  /**
   * Get attack patterns analysis
   */
  private static async getAttackPatterns(timeRangeHours: number): Promise<Array<{ pattern: string; count: number }>> {
    try {
      const patterns = [
        'rapid_sequential_requests',
        'distributed_attack_pattern',
        'credential_stuffing_attempt',
        'api_endpoint_abuse',
        'suspicious_user_agent',
        'geographical_anomaly',
      ];

      const patternCounts = [];

      for (const pattern of patterns) {
        const patternKey = `security:patterns:${pattern}`;
        const count = await redis.get(patternKey);
        if (count && parseInt(count) > 0) {
          patternCounts.push({
            pattern,
            count: parseInt(count),
          });
        }
      }

      return patternCounts.sort((a, b) => b.count - a.count);
    } catch (error) {
      logger.error('Failed to get attack patterns', {
        timeRangeHours,
        error: sanitizeError(error),
      });
      return [];
    }
  }

  /**
   * Get count of currently active attacks
   */
  private static async getActiveAttacksCount(): Promise<number> {
    try {
      const activeAttacksKey = 'security:active_attacks';
      const count = await redis.get(activeAttacksKey);
      return count ? parseInt(count) : 0;
    } catch (error) {
      logger.error('Failed to get active attacks count', {
        error: sanitizeError(error),
      });
      return 0;
    }
  }

  /**
   * Send critical alert (placeholder for notification system)
   */
  private static async sendCriticalAlert(alert: SecurityAlert): Promise<void> {
    try {
      // This would integrate with your notification system
      // For now, just log the critical alert
      logger.error(
        'CRITICAL SECURITY ALERT',
        sanitizeForLog({
          alertId: alert.id,
          type: alert.type,
          ip: alert.ip,
          description: alert.description,
          timestamp: alert.timestamp,
        }),
      );

      // Store in critical alerts queue for immediate processing
      const criticalAlertsKey = 'security:critical_alerts';
      await redis.lpush(criticalAlertsKey, JSON.stringify(alert));
      await redis.expire(criticalAlertsKey, 24 * 60 * 60); // 24 hours
    } catch (error) {
      logger.error('Failed to send critical alert', {
        alertId: alert.id,
        error: sanitizeError(error),
      });
    }
  }

  /**
   * Clean up old metrics and alerts
   */
  static async cleanupOldData(): Promise<void> {
    try {
      const now = Date.now();
      const cutoffTime = now - this.METRICS_RETENTION_DAYS * 24 * 60 * 60 * 1000;

      // Clean up old time series data
      const timeSeriesKeys = await redis.keys('security:timeseries:*');
      for (const key of timeSeriesKeys) {
        await redis.zremrangebyscore(key, '-inf', cutoffTime);
      }

      // Clean up old alerts
      const alertKeys = await redis.keys('security:alerts:*');
      for (const key of alertKeys) {
        const ttl = await redis.ttl(key);
        if (ttl === -1) {
          // No expiration set
          await redis.expire(key, this.ALERT_RETENTION_DAYS * 24 * 60 * 60);
        }
      }

      logger.info('Old security data cleaned up', {
        cutoffTime: new Date(cutoffTime),
        timeSeriesKeysProcessed: timeSeriesKeys.length,
        alertKeysProcessed: alertKeys.length,
      });
    } catch (error) {
      logger.error('Failed to cleanup old data', {
        error: sanitizeError(error),
      });
    }
  }
}
