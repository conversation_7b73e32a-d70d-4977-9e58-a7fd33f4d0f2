import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { prisma } from '../loaders/prisma';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeForLog, sanitizeError, sanitizeAuthEvent } from '../utils/logSanitizer';
import { AuthEvent } from '../types/template';
import { env } from '../config';
import * as crypto from 'crypto';

export interface MFASetupResult {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
  setupToken: string;
}

export interface MFAVerificationResult {
  success: boolean;
  message: string;
  backupCodesRemaining?: number;
}

export interface MFAContext {
  ip?: string;
  userAgent?: string;
  deviceFingerprint?: string;
}

/**
 * Encryption utilities for MFA secrets and backup codes
 */
class MFAEncryption {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  // private static readonly TAG_LENGTH = 16; // Reserved for future use

  private static getEncryptionKey(): Buffer {
    const key = env.MFA_ENCRYPTION_KEY || env.JWT_SECRET;
    if (!key) {
      throw new Error('MFA encryption key not configured');
    }
    return crypto.scryptSync(key, 'mfa-salt', this.KEY_LENGTH);
  }

  static encrypt(text: string): string {
    const key = this.getEncryptionKey();
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get the authentication tag (only for GCM mode)
    const tag = cipher.getAuthTag();

    return iv.toString('hex') + ':' + tag.toString('hex') + ':' + encrypted;
  }

  static decrypt(encryptedData: string): string {
    const key = this.getEncryptionKey();
    const parts = encryptedData.split(':');

    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format');
    }

    // Validate that all parts exist before using them
    if (!parts[0] || !parts[1] || !parts[2]) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0], 'hex');
    const tag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];

    const decipher = crypto.createDecipheriv(this.ALGORITHM, key, iv);
    // Set the authentication tag (only for GCM mode)
    decipher.setAuthTag(tag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}

/**
 * Multi-Factor Authentication Service
 */
export class MFAService {
  private readonly APP_NAME = env.MAIL_FROM_NAME || 'Secure Backend API';

  /**
   * Generate backup codes
   */
  private generateBackupCodes(count: number = 10): string[] {
    const codes: string[] = [];
    for (let i = 0; i < count; i++) {
      // Generate 8-character alphanumeric codes
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * Hash backup code for storage
   */
  private hashBackupCode(code: string): string {
    return crypto.createHash('sha256').update(code.toLowerCase()).digest('hex');
  }

  /**
   * Generate setup token for MFA setup verification
   */
  private generateSetupToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Log info with sanitization
   */
  private logInfo(message: string, data: any): void {
    logger.info(message, sanitizeForLog(data));
  }

  /**
   * Log warning with sanitization
   */
  private logWarn(message: string, data: any): void {
    logger.warn(message, sanitizeForLog(data));
  }

  /**
   * Log error with sanitization
   */
  private logError(message: string, data: any): void {
    logger.error(message, sanitizeForLog(data));
  }

  /**
   * Initialize MFA setup for a user
   */
  async initializeMFASetup(userId: string, context?: MFAContext): Promise<MFASetupResult> {
    try {
      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: { mfaSetup: true },
      });

      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      if (user.mfaSetup?.isEnabled) {
        throw new HttpException(400, 'MFA is already enabled for this user');
      }

      // Generate TOTP secret
      const secret = speakeasy.generateSecret({
        name: user.email || user.username || 'User',
        issuer: this.APP_NAME,
        length: 32,
      });

      if (!secret.base32) {
        throw new HttpException(500, 'Failed to generate MFA secret');
      }

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Generate setup token
      const setupToken = this.generateSetupToken();
      const setupExpires = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

      // Encrypt secret and backup codes
      const encryptedSecret = MFAEncryption.encrypt(secret.base32);
      const encryptedBackupCodes = backupCodes.map(code => MFAEncryption.encrypt(code));

      // Store MFA setup (not enabled yet)
      await prisma.mFASetup.upsert({
        where: { userId },
        create: {
          userId,
          secret: encryptedSecret,
          backupCodes: encryptedBackupCodes,
          qrCodeUrl,
          setupToken,
          setupExpires,
          isEnabled: false,
        },
        update: {
          secret: encryptedSecret,
          backupCodes: encryptedBackupCodes,
          qrCodeUrl,
          setupToken,
          setupExpires,
          isEnabled: false,
        },
      });

      // Log MFA setup initiation
      const authEvent: AuthEvent = {
        type: 'AUTH_MFA_SETUP_INITIATED' as any,
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('MFA setup initiated', sanitizeAuthEvent(authEvent));

      return {
        secret: secret.base32,
        qrCodeUrl,
        backupCodes,
        setupToken,
      };
    } catch (error) {
      this.logError('Failed to initialize MFA setup', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Complete MFA setup by verifying TOTP code
   */
  async completeMFASetup(
    userId: string,
    totpCode: string,
    setupToken: string,
    context?: MFAContext,
  ): Promise<MFAVerificationResult> {
    try {
      // Get MFA setup
      const mfaSetup = await prisma.mFASetup.findUnique({
        where: { userId },
        include: { user: true },
      });

      if (!mfaSetup) {
        throw new HttpException(400, 'MFA setup not found. Please start the setup process again.');
      }

      if (mfaSetup.isEnabled) {
        throw new HttpException(400, 'MFA is already enabled for this user');
      }

      // Verify setup token
      if (mfaSetup.setupToken !== setupToken) {
        throw new HttpException(400, 'Invalid setup token');
      }

      // Check if setup token has expired
      if (!mfaSetup.setupExpires || mfaSetup.setupExpires < new Date()) {
        throw new HttpException(400, 'Setup token has expired. Please start the setup process again.');
      }

      // Decrypt secret
      const secret = MFAEncryption.decrypt(mfaSetup.secret);

      // Verify TOTP code
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token: totpCode,
        window: 2, // Allow 2 time steps (60 seconds) of tolerance
      });

      if (!verified) {
        this.logWarn('Invalid TOTP code during MFA setup', {
          userId,
          ip: context?.ip,
        });
        throw new HttpException(400, 'Invalid TOTP code. Please check your authenticator app and try again.');
      }

      // Enable MFA
      await prisma.mFASetup.update({
        where: { userId },
        data: {
          isEnabled: true,
          enabledAt: new Date(),
          setupToken: null,
          setupExpires: null,
          qrCodeUrl: null, // Clear QR code after setup
        },
      });

      // Create backup code records
      const backupCodes = mfaSetup.backupCodes.map(encryptedCode => MFAEncryption.decrypt(encryptedCode));

      await prisma.mFABackupCode.createMany({
        data: backupCodes.map(code => ({
          userId,
          code: this.hashBackupCode(code),
        })),
      });

      // Log successful MFA setup
      const authEvent: AuthEvent = {
        type: 'AUTH_MFA_ENABLED' as any,
        timestamp: new Date(),
        userId: mfaSetup.user.id,
        email: mfaSetup.user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('MFA setup completed successfully', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'MFA has been successfully enabled for your account.',
        backupCodesRemaining: backupCodes.length,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logError('Failed to complete MFA setup', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Verify TOTP code for authentication
   */
  async verifyTOTP(userId: string, totpCode: string, context?: MFAContext): Promise<MFAVerificationResult> {
    try {
      // Get MFA setup
      const mfaSetup = await prisma.mFASetup.findUnique({
        where: { userId },
        include: { user: true },
      });

      if (!mfaSetup || !mfaSetup.isEnabled) {
        throw new HttpException(400, 'MFA is not enabled for this user');
      }

      // Decrypt secret
      const secret = MFAEncryption.decrypt(mfaSetup.secret);

      // Verify TOTP code
      const verified = speakeasy.totp.verify({
        secret,
        encoding: 'base32',
        token: totpCode,
        window: 2, // Allow 2 time steps (60 seconds) of tolerance
      });

      if (!verified) {
        this.logWarn('Invalid TOTP code during authentication', {
          userId,
          ip: context?.ip,
        });
        return {
          success: false,
          message: 'Invalid TOTP code. Please check your authenticator app and try again.',
        };
      }

      // Log successful MFA verification
      const authEvent: AuthEvent = {
        type: 'AUTH_MFA_VERIFIED' as any,
        timestamp: new Date(),
        userId: mfaSetup.user.id,
        email: mfaSetup.user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('MFA TOTP verification successful', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'TOTP code verified successfully.',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logError('Failed to verify TOTP code', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Verify backup code for authentication
   */
  async verifyBackupCode(userId: string, backupCode: string, context?: MFAContext): Promise<MFAVerificationResult> {
    try {
      // Get MFA setup
      const mfaSetup = await prisma.mFASetup.findUnique({
        where: { userId },
        include: { user: true },
      });

      if (!mfaSetup || !mfaSetup.isEnabled) {
        throw new HttpException(400, 'MFA is not enabled for this user');
      }

      // Hash the provided backup code
      const hashedCode = this.hashBackupCode(backupCode);

      // Find unused backup code
      const backupCodeRecord = await prisma.mFABackupCode.findFirst({
        where: {
          userId,
          code: hashedCode,
          isUsed: false,
        },
      });

      if (!backupCodeRecord) {
        this.logWarn('Invalid or used backup code during authentication', {
          userId,
          ip: context?.ip,
        });
        return {
          success: false,
          message: 'Invalid or already used backup code.',
        };
      }

      // Mark backup code as used
      await prisma.mFABackupCode.update({
        where: { id: backupCodeRecord.id },
        data: {
          isUsed: true,
          usedAt: new Date(),
        },
      });

      // Count remaining backup codes
      const remainingCodes = await prisma.mFABackupCode.count({
        where: {
          userId,
          isUsed: false,
        },
      });

      // Log successful backup code verification
      const authEvent: AuthEvent = {
        type: 'AUTH_MFA_BACKUP_CODE_USED' as any,
        timestamp: new Date(),
        userId: mfaSetup.user.id,
        email: mfaSetup.user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
          backupCodesRemaining: remainingCodes,
        },
      };
      this.logInfo('MFA backup code verification successful', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'Backup code verified successfully.',
        backupCodesRemaining: remainingCodes,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logError('Failed to verify backup code', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Disable MFA for a user
   */
  async disableMFA(userId: string, totpCode: string, context?: MFAContext): Promise<MFAVerificationResult> {
    try {
      // First verify the TOTP code
      const verification = await this.verifyTOTP(userId, totpCode, context);

      if (!verification.success) {
        return verification;
      }

      // Get user for logging
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new HttpException(404, 'User not found');
      }

      // Disable MFA and clean up
      await prisma.$transaction([
        // Delete MFA setup
        prisma.mFASetup.delete({
          where: { userId },
        }),
        // Delete all backup codes
        prisma.mFABackupCode.deleteMany({
          where: { userId },
        }),
      ]);

      // Log MFA disabled
      const authEvent: AuthEvent = {
        type: 'AUTH_MFA_DISABLED' as any,
        timestamp: new Date(),
        userId: user.id,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('MFA disabled successfully', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'MFA has been successfully disabled for your account.',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logError('Failed to disable MFA', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }

  /**
   * Get MFA status for a user
   */
  async getMFAStatus(userId: string): Promise<{
    isEnabled: boolean;
    backupCodesRemaining?: number;
    enabledAt?: Date;
  }> {
    try {
      const mfaSetup = await prisma.mFASetup.findUnique({
        where: { userId },
      });

      if (!mfaSetup || !mfaSetup.isEnabled) {
        return { isEnabled: false };
      }

      // Count remaining backup codes
      const backupCodesRemaining = await prisma.mFABackupCode.count({
        where: {
          userId,
          isUsed: false,
        },
      });

      return {
        isEnabled: true,
        backupCodesRemaining,
        enabledAt: mfaSetup.enabledAt || undefined,
      };
    } catch (error) {
      this.logError('Failed to get MFA status', {
        userId,
        error: sanitizeError(error),
      });
      throw error;
    }
  }

  /**
   * Generate new backup codes (requires TOTP verification)
   */
  async regenerateBackupCodes(
    userId: string,
    totpCode: string,
    context?: MFAContext,
  ): Promise<{
    success: boolean;
    message: string;
    backupCodes?: string[];
  }> {
    try {
      // First verify the TOTP code
      const verification = await this.verifyTOTP(userId, totpCode, context);

      if (!verification.success) {
        return verification;
      }

      // Generate new backup codes
      const newBackupCodes = this.generateBackupCodes();

      // Replace all existing backup codes
      await prisma.$transaction([
        // Delete old backup codes
        prisma.mFABackupCode.deleteMany({
          where: { userId },
        }),
        // Create new backup codes
        prisma.mFABackupCode.createMany({
          data: newBackupCodes.map(code => ({
            userId,
            code: this.hashBackupCode(code),
          })),
        }),
      ]);

      // Get user for logging
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new HttpException(404, 'An unexpected error occured');
      }

      // Log backup codes regeneration
      const authEvent: AuthEvent = {
        type: 'AUTH_MFA_BACKUP_CODES_REGENERATED' as any,
        timestamp: new Date(),
        userId: userId,
        email: user.email!,
        metadata: {
          ip: context?.ip,
          userAgent: context?.userAgent,
          deviceFingerprint: context?.deviceFingerprint,
        },
      };
      this.logInfo('MFA backup codes regenerated', sanitizeAuthEvent(authEvent));

      return {
        success: true,
        message: 'New backup codes have been generated successfully.',
        backupCodes: newBackupCodes,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logError('Failed to regenerate backup codes', {
        userId,
        error: sanitizeError(error),
        ip: context?.ip,
      });
      throw error;
    }
  }
}
