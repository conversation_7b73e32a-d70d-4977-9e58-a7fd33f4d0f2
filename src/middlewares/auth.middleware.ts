import { Request, Response, NextFunction } from 'express';
import { UserRepository } from '../repositories/user.repository';
import { HttpException } from '../exceptions/HttpException';
import { JwtUtils } from '../utils/jwt';
import { CookieUtils } from '../utils/cookieUtils';
import { TokenBlacklistService } from '../services/tokenBlacklist.service';
import { SessionManagementService } from '../services/sessionManagement.service';

// Type extensions are automatically loaded via tsconfig.json

const authMiddleware = async (req: Request, _res: Response, next: NextFunction) => {
  // Try to get token from cookies first (preferred)
  let token = CookieUtils.getAccessTokenFromCookies(req);

  // Fallback to Authorization header for backward compatibility
  if (!token) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1] || null;
    }
  }

  if (!token) {
    return next(new HttpException(401, 'Authentication token missing'));
  }

  try {
    // Check if token is blacklisted
    const tokenId = JwtUtils.getTokenId(token);
    if (tokenId && (await TokenBlacklistService.isTokenBlacklisted(tokenId))) {
      return next(new HttpException(401, 'Token has been invalidated'));
    }

    // Verify token
    const decoded = await JwtUtils.verify(token);
    const user = await UserRepository.findById(decoded.id);

    if (!user) {
      return next(new HttpException(401, 'Invalid authentication token'));
    }

    // SECURITY: Verify that the role in the token matches the current user role
    // This prevents users from retaining old privileges after role changes
    if (decoded.role !== user.role) {
      return next(new HttpException(401, 'Token role mismatch - please re-authenticate'));
    }

    // Check if this is a session-based token and validate session
    if (decoded.sessionId) {
      const session = await SessionManagementService.getSessionByToken(token);

      if (!session || !session.isActive) {
        return next(new HttpException(401, 'Session has expired or been terminated'));
      }

      // Check if session has expired
      if (session.expiresAt < new Date()) {
        await SessionManagementService.terminateSession(session.id, 'system', 'expired');
        return next(new HttpException(401, 'Session has expired'));
      }

      // Update session activity
      const context = SessionManagementService.extractSessionContext(req);
      await SessionManagementService.updateSessionActivity(token, context);

      // Add session info to request
      (req as any).sessionId = session.id;
      (req as any).sessionToken = token;
      (req as any).deviceId = session.deviceId;
    }

    req.user = user;
    next();
  } catch {
    next(new HttpException(401, 'Invalid authentication token'));
  }
};

export default authMiddleware;
