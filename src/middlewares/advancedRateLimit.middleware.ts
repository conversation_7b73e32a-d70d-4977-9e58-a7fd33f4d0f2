import { Request, Response, NextFunction } from 'express';
import {
  AdvancedRateLimitService,
  AdaptiveRateLimitConfig,
  DDoSDetectionConfig,
} from '../services/advancedRateLimit.service';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';

/**
 * Advanced Rate Limiting Middleware
 * Provides sophisticated rate limiting with adaptive algorithms and DDoS protection
 */
export class AdvancedRateLimitMiddleware {
  /**
   * Adaptive rate limiting middleware
   */
  static adaptive(config: AdaptiveRateLimitConfig) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const context = AdvancedRateLimitService.extractRequestContext(req);

        // Skip if no user ID available (for authenticated endpoints)
        if (!context.userId) {
          return next();
        }

        const identifier = `${context.ip}_${context.userId}`;

        const result = await AdvancedRateLimitService.adaptiveRateLimit(config, context.userId, context.ip, identifier);

        if (!result.allowed) {
          // Set rate limit headers
          res.set({
            'X-RateLimit-Limit': config.maxPoints.toString(),
            'X-RateLimit-Remaining': result.remainingPoints.toString(),
            'X-RateLimit-Reset': result.resetTime.toISOString(),
            'Retry-After': result.retryAfter?.toString() || '60',
          });

          logger.warn('Adaptive rate limit exceeded', {
            ip: context.ip,
            userId: context.userId,
            endpoint: context.endpoint,
            method: context.method,
            remainingPoints: result.remainingPoints,
            retryAfter: result.retryAfter,
            reason: result.reason,
          });

          return next(new HttpException(429, result.reason || 'Rate limit exceeded'));
        }

        // Set success headers
        res.set({
          'X-RateLimit-Limit': config.maxPoints.toString(),
          'X-RateLimit-Remaining': result.remainingPoints.toString(),
          'X-RateLimit-Reset': result.resetTime.toISOString(),
        });

        next();
      } catch (error) {
        logger.error('Adaptive rate limiting failed', {
          ip: req.ip,
          endpoint: req.path,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        // Fail open - allow request if rate limiting fails
        next();
      }
    };
  }

  /**
   * DDoS protection middleware
   */
  static ddosProtection(config?: Partial<DDoSDetectionConfig>) {
    const ddosConfig: DDoSDetectionConfig = {
      requestThreshold: 1000,
      timeWindow: 60,
      blockDuration: 300,
      suspiciousPatternThreshold: 0.8,
      ...config,
    };

    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const context = AdvancedRateLimitService.extractRequestContext(req);

        // Check if IP is already blocked
        const blockStatus = await AdvancedRateLimitService.isIPBlocked(context.ip);

        if (blockStatus.blocked) {
          res.set({
            'X-Blocked-Until': blockStatus.expiresAt?.toISOString() || 'unknown',
            'X-Block-Reason': blockStatus.reason || 'DDoS protection',
          });

          logger.warn('Blocked IP attempted access', {
            ip: context.ip,
            endpoint: context.endpoint,
            reason: blockStatus.reason,
            expiresAt: blockStatus.expiresAt,
          });

          return next(new HttpException(429, 'IP temporarily blocked due to suspicious activity'));
        }

        // Perform DDoS detection
        const ddosResult = await AdvancedRateLimitService.detectDDoSAttack(
          context.ip,
          context.userAgent,
          context.endpoint,
          ddosConfig,
        );

        if (ddosResult.isDDoS) {
          res.set({
            'X-DDoS-Protection': 'active',
            'X-Block-Duration': ddosResult.blockDuration?.toString() || '300',
          });

          logger.error('DDoS attack detected and blocked', {
            ip: context.ip,
            userAgent: context.userAgent,
            endpoint: context.endpoint,
            blockDuration: ddosResult.blockDuration,
            reason: ddosResult.reason,
          });

          return next(new HttpException(429, ddosResult.reason || 'DDoS attack detected'));
        }

        next();
      } catch (error) {
        logger.error('DDoS protection failed', {
          ip: req.ip,
          endpoint: req.path,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        // Fail open - allow request if DDoS protection fails
        next();
      }
    };
  }

  /**
   * Smart rate limiting that combines multiple strategies
   */
  static smart(options: {
    adaptive?: AdaptiveRateLimitConfig;
    ddos?: Partial<DDoSDetectionConfig>;
    enableDDoSProtection?: boolean;
    enableAdaptive?: boolean;
  }) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        const context = AdvancedRateLimitService.extractRequestContext(req);

        // 1. DDoS Protection (if enabled)
        if (options.enableDDoSProtection !== false) {
          const blockStatus = await AdvancedRateLimitService.isIPBlocked(context.ip);

          if (blockStatus.blocked) {
            res.set({
              'X-Blocked-Until': blockStatus.expiresAt?.toISOString() || 'unknown',
              'X-Block-Reason': blockStatus.reason || 'DDoS protection',
            });

            return next(new HttpException(429, 'IP temporarily blocked due to suspicious activity'));
          }

          const ddosConfig: DDoSDetectionConfig = {
            requestThreshold: 500,
            timeWindow: 60,
            blockDuration: 300,
            suspiciousPatternThreshold: 0.7,
            ...options.ddos,
          };

          const ddosResult = await AdvancedRateLimitService.detectDDoSAttack(
            context.ip,
            context.userAgent,
            context.endpoint,
            ddosConfig,
          );

          if (ddosResult.isDDoS) {
            return next(new HttpException(429, ddosResult.reason || 'DDoS attack detected'));
          }
        }

        // 2. Adaptive Rate Limiting (if enabled and user is authenticated)
        if (options.enableAdaptive !== false && context.userId && options.adaptive) {
          const identifier = `${context.ip}_${context.userId}`;

          const result = await AdvancedRateLimitService.adaptiveRateLimit(
            options.adaptive,
            context.userId,
            context.ip,
            identifier,
          );

          if (!result.allowed) {
            res.set({
              'X-RateLimit-Limit': options.adaptive.maxPoints.toString(),
              'X-RateLimit-Remaining': result.remainingPoints.toString(),
              'X-RateLimit-Reset': result.resetTime.toISOString(),
              'Retry-After': result.retryAfter?.toString() || '60',
            });

            return next(new HttpException(429, result.reason || 'Rate limit exceeded'));
          }

          // Set success headers
          res.set({
            'X-RateLimit-Limit': options.adaptive.maxPoints.toString(),
            'X-RateLimit-Remaining': result.remainingPoints.toString(),
            'X-RateLimit-Reset': result.resetTime.toISOString(),
          });
        }

        next();
      } catch (error) {
        logger.error('Smart rate limiting failed', {
          ip: req.ip,
          endpoint: req.path,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        // Fail open - allow request if rate limiting fails
        next();
      }
    };
  }

  /**
   * API endpoint protection with different levels
   */
  static protectEndpoint(level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL') {
    const configs = {
      LOW: {
        adaptive: {
          points: 100, // Default points value
          basePoints: 100,
          maxPoints: 200,
          adaptiveMultiplier: 1.5,
          trustScoreThreshold: 0.7,
          duration: 60,
          blockDuration: 60,
          keyPrefix: 'rl_low',
        },
        ddos: {
          requestThreshold: 1000,
          timeWindow: 60,
          blockDuration: 300,
          suspiciousPatternThreshold: 0.8,
        },
      },
      MEDIUM: {
        adaptive: {
          points: 50, // Default points value
          basePoints: 50,
          maxPoints: 100,
          adaptiveMultiplier: 1.3,
          trustScoreThreshold: 0.6,
          duration: 60,
          blockDuration: 120,
          keyPrefix: 'rl_medium',
        },
        ddos: {
          requestThreshold: 500,
          timeWindow: 60,
          blockDuration: 600,
          suspiciousPatternThreshold: 0.7,
        },
      },
      HIGH: {
        adaptive: {
          points: 20, // Default points value
          basePoints: 20,
          maxPoints: 50,
          adaptiveMultiplier: 1.2,
          trustScoreThreshold: 0.5,
          duration: 60,
          blockDuration: 300,
          keyPrefix: 'rl_high',
        },
        ddos: {
          requestThreshold: 200,
          timeWindow: 60,
          blockDuration: 900,
          suspiciousPatternThreshold: 0.6,
        },
      },
      CRITICAL: {
        adaptive: {
          points: 5, // Default points value
          basePoints: 5,
          maxPoints: 15,
          adaptiveMultiplier: 1.1,
          trustScoreThreshold: 0.8,
          duration: 60,
          blockDuration: 600,
          keyPrefix: 'rl_critical',
        },
        ddos: {
          requestThreshold: 50,
          timeWindow: 60,
          blockDuration: 1800,
          suspiciousPatternThreshold: 0.5,
        },
      },
    };

    const config = configs[level];

    return this.smart({
      adaptive: config.adaptive,
      ddos: config.ddos,
      enableDDoSProtection: true,
      enableAdaptive: true,
    });
  }

  /**
   * Rate limiting for authentication endpoints
   */
  static authProtection() {
    return this.protectEndpoint('HIGH');
  }

  /**
   * Rate limiting for sensitive operations
   */
  static sensitiveOperation() {
    return this.protectEndpoint('CRITICAL');
  }

  /**
   * Rate limiting for public API endpoints
   */
  static publicAPI() {
    return this.protectEndpoint('MEDIUM');
  }

  /**
   * Rate limiting for general endpoints
   */
  static general() {
    return this.protectEndpoint('LOW');
  }
}
