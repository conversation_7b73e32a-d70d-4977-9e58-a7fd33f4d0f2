import { plainToInstance } from 'class-transformer';
import { validate, ValidationError } from 'class-validator';
import { Request, Response, NextFunction } from 'express';
import { HttpException } from '../exceptions/HttpException';

// Custom validation error class
class ValidationException extends HttpException {
  public validationErrors: ValidationError[];

  constructor(validationErrors: ValidationError[]) {
    const message = 'Validation failed';
    super(400, message);
    this.name = 'ValidationException';
    this.validationErrors = validationErrors;
  }

  public getFormattedErrors(): Record<string, string[]> {
    const formattedErrors: Record<string, string[]> = {};

    this.validationErrors.forEach((error: ValidationError) => {
      if (error.property && error.constraints) {
        formattedErrors[error.property] = Object.values(error.constraints);
      }
    });

    return formattedErrors;
  }
}

function validationMiddleware(
  type: new () => object,
  skipMissingProperties = false,
): (req: Request, res: Response, next: NextFunction) => void {
  return (req, _res, next) => {
    const dto = plainToInstance(type, req.body);

    validate(dto, { skipMissingProperties })
      .then((errors: ValidationError[]) => {
        if (errors.length > 0) {
          next(new ValidationException(errors));
        } else {
          next();
        }
      })
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      .catch(_error => {
        // Handle unexpected validation errors
        next(new HttpException(500, 'Validation processing failed'));
      });
  };
}

export default validationMiddleware;
export { ValidationException };
