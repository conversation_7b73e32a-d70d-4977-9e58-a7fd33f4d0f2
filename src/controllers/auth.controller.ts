import { Request, Response, NextFunction } from 'express';
import { AuthService, AuthContext } from '../services/auth.service';
import { EmailVerificationService, EmailVerificationContext } from '../services/emailVerification.service';
import { PasswordResetService, PasswordResetContext } from '../services/passwordReset.service';
import { RBACService } from '../services/rbac.service';
import { Resource, Action } from '../types/rbac';
import { ErrorHandler, ErrorCodes } from '../utils/errorHandler';
import { HttpException } from '../exceptions/HttpException';
import { CookieUtils } from '../utils/cookieUtils';
import { CreateUserDto } from '../dtos/user.dto';
import { LoginDto, ForgotPasswordDto, ResetPasswordDto } from '../dtos/auth.dto';
import { User as UserInterface } from '../interfaces/user.interface';

export class AuthController {
  /**
   * Register a new user with cookies-only token delivery
   */
  static async signUp(req: Request, res: Response, next: NextFunction) {
    try {
      const userData: CreateUserDto = req.body;
      const authContext: AuthContext = {};
      if (req.ip) authContext.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) authContext.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) authContext.deviceFingerprint = deviceFingerprint;

      const result = await AuthService.signUp(userData, authContext);

      // Set secure HTTP-only cookies for both tokens
      CookieUtils.setTokenCookies(res, result.token, result.refreshToken!);

      // Return only user data and metadata (no tokens in JSON)
      res.status(201).json({
        success: true,
        message: 'User registered successfully. Please check your email for verification.',
        data: {
          user: result.user,
          expiresIn: result.expiresIn,
          tokenType: result.tokenType,
          emailVerificationRequired: result.emailVerificationRequired,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Login user with cookies-only token delivery
   */
  static async logIn(req: Request, res: Response, next: NextFunction) {
    try {
      const loginData: LoginDto = req.body;
      const authContext: AuthContext = {};
      if (req.ip) authContext.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) authContext.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) authContext.deviceFingerprint = deviceFingerprint;

      const result = await AuthService.logIn(loginData, authContext);

      // Set secure HTTP-only cookies for both tokens
      CookieUtils.setTokenCookies(res, result.token, result.refreshToken!);

      // Return only user data and metadata (no tokens in JSON)
      res.status(200).json({
        success: true,
        message: 'User logged in successfully',
        data: {
          user: result.user,
          expiresIn: result.expiresIn,
          tokenType: result.tokenType,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const user = await AuthService.getProfile((req.user as UserInterface).id);

      res.status(200).json({
        success: true,
        data: user,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update current user profile
   */
  static async updateProfile(req: Request, res: Response, next: NextFunction) {
    try {
      const user = await AuthService.updateProfile((req.user as UserInterface).id, req.body);

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: user,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Change current user password
   */
  static async changePassword(req: Request, res: Response, next: NextFunction) {
    try {
      const authContext: AuthContext = {};
      if (req.ip) authContext.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) authContext.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) authContext.deviceFingerprint = deviceFingerprint;

      await AuthService.changePassword((req.user as UserInterface).id, req.body, authContext);

      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all users (Admin only)
   */
  static async getAllUsers(req: Request, res: Response, next: NextFunction) {
    try {
      const page = parseInt(req.query['page'] as string) || 1;
      const limit = parseInt(req.query['limit'] as string) || 10;
      const filters = req.query['filters'] ? JSON.parse(req.query['filters'] as string) : {};

      const result = await AuthService.getAllUsers(page, limit, filters);

      res.status(200).json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user role (Admin only)
   */
  static async updateUserRole(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;
      const { role } = req.body;

      if (!userId) {
        throw new HttpException(400, 'User ID is required');
      }

      const user = await AuthService.updateUserRole(userId, role);

      res.status(200).json({
        success: true,
        message: 'User role updated successfully',
        data: user,
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete user (Admin only)
   */
  static async deleteUser(req: Request, res: Response, next: NextFunction) {
    try {
      const { userId } = req.params;

      if (!userId) {
        throw new HttpException(400, 'User ID is required');
      }

      await AuthService.deleteUser(userId);

      res.status(200).json({
        success: true,
        message: 'User deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user permissions (demonstrates RBAC usage)
   */
  static async getUserPermissions(req: Request, res: Response, next: NextFunction) {
    try {
      const { user } = req;
      if (!user) {
        throw ErrorHandler.unauthorized('User data missing', ErrorCodes.TOKEN_INVALID);
      }

      // Get all resources the user can access
      const userInterface = user as UserInterface;
      const accessibleResources = Object.values(Resource).filter(resource =>
        RBACService.canAccessResource(userInterface, resource),
      );

      // Get allowed actions for each accessible resource
      const permissions = accessibleResources.reduce(
        (acc, resource) => {
          acc[resource] = RBACService.getAllowedActions(userInterface, resource);
          return acc;
        },
        {} as Record<Resource, Action[]>,
      );

      res.status(200).json({
        success: true,
        data: {
          user: {
            id: userInterface.id,
            email: userInterface.email,
            role: userInterface.role,
          },
          accessibleResources,
          permissions,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check specific permission (demonstrates RBAC usage)
   */
  static async checkPermission(req: Request, res: Response, next: NextFunction) {
    try {
      const { resource, action } = req.params;
      const { user } = req;
      if (!user) {
        throw ErrorHandler.unauthorized('User data missing', ErrorCodes.TOKEN_INVALID);
      }

      const userInterface = user as UserInterface;
      const permissionResult = RBACService.checkPermission(userInterface, resource as Resource, action as Action);

      res.status(200).json({
        success: true,
        data: {
          user: {
            id: userInterface.id,
            email: userInterface.email,
            role: userInterface.role,
          },
          resource,
          action,
          result: permissionResult,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(req: Request, res: Response, next: NextFunction) {
    try {
      // Get refresh token from cookies
      const refreshToken = CookieUtils.getRefreshTokenFromCookies(req);

      if (!refreshToken) {
        throw new HttpException(400, 'Refresh token is required');
      }

      const result = await AuthService.refreshToken(refreshToken);

      // Set secure HTTP-only cookies for new tokens
      CookieUtils.setTokenCookies(res, result.token, result.refreshToken!);

      // Return only user data and metadata (no tokens in JSON)
      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          user: result.user,
          expiresIn: result.expiresIn,
          tokenType: result.tokenType,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout user, clear cookies, and blacklist tokens
   */
  static async logOut(req: Request, res: Response, next: NextFunction) {
    try {
      // Blacklist both tokens upon logout
      const accessToken = CookieUtils.getAccessTokenFromCookies(req) || undefined;
      const refreshToken = CookieUtils.getRefreshTokenFromCookies(req) || undefined;
      await AuthService.logout(accessToken, refreshToken);

      // Clear all cookies
      CookieUtils.clearTokenCookies(res);

      res.status(200).json({
        success: true,
        message: 'Logged out successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify email using token (handles both new user verification and email change)
   */
  static async verifyEmail(req: Request, res: Response, next: NextFunction) {
    try {
      const { token } = req.body.token ? req.body : req.query;

      if (!token) {
        throw new HttpException(400, 'Verification token is required');
      }

      const context: EmailVerificationContext = {};
      if (req.ip) context.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) context.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) context.deviceFingerprint = deviceFingerprint;

      const emailVerificationService = new EmailVerificationService();
      const result = await emailVerificationService.verifyEmail(token, context);

      res.status(200).json({
        success: true,
        message: result.message,
        data: {
          user: result.user,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Resend email verification (authenticated or by email)
   */
  static async resendVerification(req: Request, res: Response, next: NextFunction) {
    try {
      const { email } = req.body;
      const user = req.user; // May be undefined for unauthenticated requests

      let targetUserId: string;

      if (user) {
        // Authenticated user - resend verification for their account
        targetUserId = (user as UserInterface).id;
      } else if (email) {
        // Unauthenticated user - find user by email
        const foundUser = await AuthService.findUserByEmail(email);
        if (!foundUser) {
          throw new HttpException(400, 'User not found');
        }
        targetUserId = foundUser.id;
      } else {
        throw new HttpException(400, 'Email is required for unauthenticated requests');
      }

      const context: EmailVerificationContext = {};
      if (req.ip) context.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) context.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) context.deviceFingerprint = deviceFingerprint;

      const emailVerificationService = new EmailVerificationService();
      const result = await emailVerificationService.sendEmailVerification(targetUserId, context);

      if (result.canResendAfter) {
        res.status(429).json({
          success: false,
          message: result.message,
          data: {
            canResendAfter: result.canResendAfter,
          },
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(200).json({
          success: true,
          message: result.message,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Change email address (authenticated route)
   */
  static async changeEmail(req: Request, res: Response, next: NextFunction) {
    try {
      const { newEmail } = req.body;
      const user = req.user!; // Guaranteed to exist due to authMiddleware

      if (!newEmail) {
        throw new HttpException(400, 'New email address is required');
      }

      if (newEmail === (user as UserInterface).email) {
        throw new HttpException(400, 'New email address must be different from current email');
      }

      const context: EmailVerificationContext = {};
      if (req.ip) context.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) context.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) context.deviceFingerprint = deviceFingerprint;

      const emailVerificationService = new EmailVerificationService();
      const result = await emailVerificationService.sendEmailChangeVerification(
        (user as UserInterface).id,
        newEmail,
        context,
      );

      if (result.canResendAfter) {
        res.status(429).json({
          success: false,
          message: result.message,
          data: {
            canResendAfter: result.canResendAfter,
          },
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(200).json({
          success: true,
          message: result.message,
          data: {
            pendingEmail: newEmail,
            currentEmail: (user as UserInterface).email,
          },
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initiate password reset process
   */
  static async forgotPassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { email, username }: ForgotPasswordDto = req.body;
      const identifier = email || username;

      if (!identifier) {
        throw new HttpException(400, 'Email or username is required');
      }

      const context: PasswordResetContext = {};
      if (req.ip) context.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) context.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) context.deviceFingerprint = deviceFingerprint;

      const passwordResetService = new PasswordResetService();
      const result = await passwordResetService.initiatePasswordReset(identifier, context);

      if (result.canResendAfter) {
        res.status(429).json({
          success: false,
          message: result.message,
          data: {
            canResendAfter: result.canResendAfter,
          },
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(200).json({
          success: true,
          message: result.message,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset password using token
   */
  static async resetPassword(req: Request, res: Response, next: NextFunction) {
    try {
      const { token, newPassword }: ResetPasswordDto = req.body;

      const context: PasswordResetContext = {};
      if (req.ip) context.ip = req.ip;
      const userAgent = req.get('User-Agent');
      if (userAgent) context.userAgent = userAgent;
      const deviceFingerprint = (req as unknown as Record<string, unknown>)['deviceFingerprint'] as string | undefined;
      if (deviceFingerprint) context.deviceFingerprint = deviceFingerprint;

      const passwordResetService = new PasswordResetService();
      const result = await passwordResetService.resetPassword(token, newPassword, context);

      res.status(200).json({
        success: true,
        message: result.message,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Validate password reset token
   */
  static async validateResetToken(req: Request, res: Response, next: NextFunction) {
    try {
      const { token } = req.query;

      if (!token || typeof token !== 'string') {
        throw new HttpException(400, 'Reset token is required');
      }

      const passwordResetService = new PasswordResetService();
      const result = await passwordResetService.validateResetToken(token);

      res.status(200).json({
        success: true,
        data: {
          valid: result.valid,
          expired: result.expired,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
}
