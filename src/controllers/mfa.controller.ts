import { Request, Response, NextFunction } from 'express';
import { MFAService } from '../services/mfa.service';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeForLog } from '../utils/logSanitizer';

export class MFAController {
  private static mfaService = new MFAService();
  /**
   * Get MFA status for the authenticated user
   */
  static async getStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const status = await this.mfaService.getMFAStatus(userId);

      res.status(200).json({
        success: true,
        data: status,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initialize MFA setup process
   */
  static async initializeSetup(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).user?.id;

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const setupData = await this.mfaService.initializeMFASetup(userId);

      logger.info(
        'MFA setup initialized',
        sanitizeForLog({
          userId,
          setupToken: setupData.setupToken,
        }),
      );

      res.status(200).json({
        success: true,
        data: setupData,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Complete MFA setup with TOTP verification
   */
  static async completeSetup(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).user?.id;
      const { setupToken, totpCode } = req.body;

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      if (!setupToken || !totpCode) {
        throw new HttpException(400, 'Setup token and TOTP code are required');
      }

      const result = await this.mfaService.completeMFASetup(userId, totpCode, setupToken);

      logger.info(
        'MFA setup completed',
        sanitizeForLog({
          userId,
          backupCodesGenerated: result.backupCodesRemaining,
        }),
      );

      res.status(200).json({
        success: true,
        message: 'MFA setup completed successfully',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify TOTP code during login
   */
  static async verifyTOTP(req: Request, res: Response, next: NextFunction) {
    try {
      const { totpCode } = req.body;
      const userId = (req as any).user?.id;

      if (!totpCode) {
        throw new HttpException(400, 'TOTP code is required');
      }

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const result = await this.mfaService.verifyTOTP(userId, totpCode);

      logger.info(
        'TOTP verification successful',
        sanitizeForLog({
          userId,
          success: result.success,
        }),
      );

      res.status(200).json({
        success: true,
        message: 'TOTP verification successful',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify backup code during login
   */
  static async verifyBackupCode(req: Request, res: Response, next: NextFunction) {
    try {
      const { backupCode } = req.body;
      const userId = (req as any).user?.id;

      if (!backupCode) {
        throw new HttpException(400, 'Backup code is required');
      }

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const result = await this.mfaService.verifyBackupCode(userId, backupCode);

      logger.info(
        'Backup code verification successful',
        sanitizeForLog({
          userId,
          success: result.success,
          remainingBackupCodes: result.backupCodesRemaining,
        }),
      );

      res.status(200).json({
        success: true,
        message: 'Backup code verification successful',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Disable MFA for the authenticated user
   */
  static async disableMFA(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).user?.id;
      const { totpCode } = req.body;

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      if (!totpCode) {
        throw new HttpException(400, 'TOTP code is required to disable MFA');
      }

      await this.mfaService.disableMFA(userId, totpCode);

      logger.info(
        'MFA disabled',
        sanitizeForLog({
          userId,
        }),
      );

      res.status(200).json({
        success: true,
        message: 'MFA disabled successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Regenerate backup codes
   */
  static async regenerateBackupCodes(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).user?.id;
      const { totpCode } = req.body;

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      if (!totpCode) {
        throw new HttpException(400, 'TOTP code is required to regenerate backup codes');
      }

      const result = await this.mfaService.regenerateBackupCodes(userId, totpCode);

      logger.info(
        'Backup codes regenerated',
        sanitizeForLog({
          userId,
          newBackupCodesCount: result.backupCodes?.length || 0,
        }),
      );

      res.status(200).json({
        success: true,
        message: 'Backup codes regenerated successfully',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
}
