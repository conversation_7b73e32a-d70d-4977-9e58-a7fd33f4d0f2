import { Request, Response, NextFunction } from 'express';
import { DDoSMonitoringService } from '../services/ddosMonitoring.service';
import { AdvancedRateLimitService } from '../services/advancedRateLimit.service';
import { HttpException } from '../exceptions/HttpException';
import { redis } from '../config/redis';

export class SecurityController {
  /**
   * Get DDoS attack metrics and statistics
   */
  static async getDDoSMetrics(req: Request, res: Response, next: NextFunction) {
    try {
      const timeRangeHours = parseInt(req.query['hours'] as string) || 24;

      if (timeRangeHours < 1 || timeRangeHours > 168) {
        // Max 1 week
        throw new HttpException(400, 'Time range must be between 1 and 168 hours');
      }

      const metrics = await DDoSMonitoringService.getAttackMetrics(timeRangeHours);

      res.status(200).json({
        success: true,
        data: {
          metrics,
          timeRange: `${timeRangeHours} hours`,
          generatedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get rate limiting statistics
   */
  static async getRateLimitStats(req: Request, res: Response, next: NextFunction) {
    try {
      const timeRangeHours = parseInt(req.query['hours'] as string) || 24;

      if (timeRangeHours < 1 || timeRangeHours > 168) {
        throw new HttpException(400, 'Time range must be between 1 and 168 hours');
      }

      const stats = await DDoSMonitoringService.getRateLimitStats(timeRangeHours);

      res.status(200).json({
        success: true,
        data: {
          endpoints: stats,
          totalEndpoints: stats.length,
          timeRange: `${timeRangeHours} hours`,
          generatedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get security alerts
   */
  static async getSecurityAlerts(req: Request, res: Response, next: NextFunction) {
    try {
      const limit = parseInt(req.query['limit'] as string) || 50;
      const severity = req.query['severity'] as 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' | undefined;

      if (limit < 1 || limit > 1000) {
        throw new HttpException(400, 'Limit must be between 1 and 1000');
      }

      if (severity && !['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'].includes(severity)) {
        throw new HttpException(400, 'Invalid severity level');
      }

      const alerts = await DDoSMonitoringService.getSecurityAlerts(limit, severity);

      res.status(200).json({
        success: true,
        data: {
          alerts,
          totalAlerts: alerts.length,
          filters: {
            limit,
            severity: severity || 'all',
          },
          generatedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if an IP is blocked
   */
  static async checkIPStatus(req: Request, res: Response, next: NextFunction) {
    try {
      const { ip } = req.params;

      if (!ip) {
        throw new HttpException(400, 'IP address is required');
      }

      // Validate IP format (basic validation)
      const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
      if (!ipRegex.test(ip)) {
        throw new HttpException(400, 'Invalid IP address format');
      }

      const blockStatus = await AdvancedRateLimitService.isIPBlocked(ip);

      res.status(200).json({
        success: true,
        data: {
          ip,
          blocked: blockStatus.blocked,
          reason: blockStatus.reason,
          expiresAt: blockStatus.expiresAt,
          checkedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Manually block an IP address
   */
  static async blockIP(req: Request, res: Response, next: NextFunction) {
    try {
      const { ip, reason, duration } = req.body;

      if (!ip) {
        throw new HttpException(400, 'IP address is required');
      }

      // Validate IP format
      const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
      if (!ipRegex.test(ip)) {
        throw new HttpException(400, 'Invalid IP address format');
      }

      const blockDuration = duration || 3600; // Default 1 hour
      const blockReason = reason || 'Manual block by administrator';

      // Block the IP
      const blockKey = `ddos:blocked:${ip}`;
      await redis.setex(
        blockKey,
        blockDuration,
        JSON.stringify({
          blockedAt: Date.now(),
          reason: blockReason,
          blockedBy: (req as any).user?.id || 'system',
          manual: true,
        }),
      );

      // Record security event
      await DDoSMonitoringService.recordSecurityEvent('IP_BLOCKED', 'HIGH', ip, `IP manually blocked: ${blockReason}`, {
        blockedBy: (req as any).user?.id || 'system',
        duration: blockDuration,
        manual: true,
      });

      res.status(200).json({
        success: true,
        message: 'IP address blocked successfully',
        data: {
          ip,
          reason: blockReason,
          duration: blockDuration,
          expiresAt: new Date(Date.now() + blockDuration * 1000).toISOString(),
          blockedBy: (req as any).user?.id || 'system',
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Manually unblock an IP address
   */
  static async unblockIP(req: Request, res: Response, next: NextFunction) {
    try {
      const { ip } = req.params;

      if (!ip) {
        throw new HttpException(400, 'IP address is required');
      }

      // Validate IP format
      const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
      if (!ipRegex.test(ip)) {
        throw new HttpException(400, 'Invalid IP address format');
      }

      // Check if IP is blocked
      const blockKey = `ddos:blocked:${ip}`;
      const blockData = await redis.get(blockKey);

      if (!blockData) {
        throw new HttpException(404, 'IP address is not currently blocked');
      }

      // Remove the block
      await redis.del(blockKey);

      // Record security event
      await DDoSMonitoringService.recordSecurityEvent(
        'IP_BLOCKED',
        'MEDIUM',
        ip,
        'IP manually unblocked by administrator',
        {
          unblockedBy: (req as any).user?.id || 'system',
          manual: true,
        },
      );

      res.status(200).json({
        success: true,
        message: 'IP address unblocked successfully',
        data: {
          ip,
          unblockedBy: (req as any).user?.id || 'system',
          unblockedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get system security status
   */
  static async getSecurityStatus(_req: Request, res: Response, next: NextFunction) {
    try {
      // Get basic metrics
      const metrics = await DDoSMonitoringService.getAttackMetrics(1); // Last hour
      const rateLimitStats = await DDoSMonitoringService.getRateLimitStats(1);
      const recentAlerts = await DDoSMonitoringService.getSecurityAlerts(10, 'HIGH');

      // Get Redis health
      const redisHealthy = (await redis.ping()) === 'PONG';

      // Calculate security score
      let securityScore = 100;

      if (metrics.activeAttacks > 0) securityScore -= 20;
      if (metrics.blockedIPs > 100) securityScore -= 15;
      if (recentAlerts.length > 5) securityScore -= 10;
      if (!redisHealthy) securityScore -= 30;

      const status =
        securityScore >= 80
          ? 'HEALTHY'
          : securityScore >= 60
            ? 'WARNING'
            : securityScore >= 40
              ? 'CRITICAL'
              : 'EMERGENCY';

      res.status(200).json({
        success: true,
        data: {
          status,
          securityScore,
          metrics: {
            activeAttacks: metrics.activeAttacks,
            blockedIPs: metrics.blockedIPs,
            totalAttacks: metrics.totalAttacks,
            recentHighAlerts: recentAlerts.length,
          },
          systemHealth: {
            redis: redisHealthy,
            rateLimiting: rateLimitStats.length > 0,
          },
          lastUpdated: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user trust score
   */
  static async getUserTrustScore(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = (req as any).user?.id;
      const ip = req.ip || 'unknown';

      if (!userId) {
        throw new HttpException(401, 'User not authenticated');
      }

      const trustScore = await AdvancedRateLimitService.calculateUserTrustScore(userId, ip);

      res.status(200).json({
        success: true,
        data: {
          userId,
          trustScore,
          trustLevel:
            trustScore >= 0.8 ? 'HIGH' : trustScore >= 0.6 ? 'MEDIUM' : trustScore >= 0.4 ? 'LOW' : 'VERY_LOW',
          calculatedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Trigger security data cleanup
   */
  static async cleanupSecurityData(_req: Request, res: Response, next: NextFunction) {
    try {
      await DDoSMonitoringService.cleanupOldData();

      res.status(200).json({
        success: true,
        message: 'Security data cleanup completed successfully',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
}
