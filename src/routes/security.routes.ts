import { Router } from 'express';
import { authMiddleware, detectSuspiciousActivity, validationMiddleware } from '@/middlewares';
import { IsString, IsOptional, <PERSON>N<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { AdvancedRateLimitMiddleware } from '@/middlewares/advancedRateLimit.middleware';
import { SecurityController } from '@/controllers/security.controller';

// DTOs for validation
class BlockIPDto {
  @IsString()
  ip!: string;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(86400)
  duration?: number;
}

const router = Router();

/**
 * @swagger
 * /security/status:
 *   get:
 *     summary: Get overall security system status
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Security status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [HEALTHY, WARNING, CRITICAL, EMERGENCY]
 *                     securityScore:
 *                       type: number
 *                     metrics:
 *                       type: object
 *                     systemHealth:
 *                       type: object
 *       401:
 *         description: Unauthorized
 */
router.get('/status', authMiddleware, AdvancedRateLimitMiddleware.general(), SecurityController.getSecurityStatus);

/**
 * @swagger
 * /security/ddos/metrics:
 *   get:
 *     summary: Get DDoS attack metrics and statistics
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: hours
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 168
 *         description: Time range in hours (default 24, max 168)
 *     responses:
 *       200:
 *         description: DDoS metrics retrieved successfully
 *       400:
 *         description: Invalid time range
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/ddos/metrics',
  authMiddleware,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  SecurityController.getDDoSMetrics,
);

/**
 * @swagger
 * /security/rate-limit/stats:
 *   get:
 *     summary: Get rate limiting statistics
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: hours
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 168
 *         description: Time range in hours (default 24, max 168)
 *     responses:
 *       200:
 *         description: Rate limiting stats retrieved successfully
 *       400:
 *         description: Invalid time range
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/rate-limit/stats',
  authMiddleware,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  SecurityController.getRateLimitStats,
);

/**
 * @swagger
 * /security/alerts:
 *   get:
 *     summary: Get security alerts
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *         description: Number of alerts to return (default 50, max 1000)
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *         description: Filter by severity level
 *     responses:
 *       200:
 *         description: Security alerts retrieved successfully
 *       400:
 *         description: Invalid parameters
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/alerts',
  authMiddleware,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  SecurityController.getSecurityAlerts,
);

/**
 * @swagger
 * /security/ip/{ip}/status:
 *   get:
 *     summary: Check if an IP address is blocked
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ip
 *         required: true
 *         schema:
 *           type: string
 *         description: IP address to check
 *     responses:
 *       200:
 *         description: IP status retrieved successfully
 *       400:
 *         description: Invalid IP address format
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/ip/:ip/status',
  authMiddleware,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  SecurityController.checkIPStatus,
);

/**
 * @swagger
 * /security/ip/block:
 *   post:
 *     summary: Manually block an IP address
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ip:
 *                 type: string
 *                 description: IP address to block
 *               reason:
 *                 type: string
 *                 description: Reason for blocking
 *               duration:
 *                 type: integer
 *                 minimum: 60
 *                 maximum: 86400
 *                 description: Block duration in seconds (default 3600)
 *             required:
 *               - ip
 *     responses:
 *       200:
 *         description: IP blocked successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/ip/block',
  authMiddleware,
  detectSuspiciousActivity,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  validationMiddleware(BlockIPDto),
  SecurityController.blockIP,
);

/**
 * @swagger
 * /security/ip/{ip}/unblock:
 *   delete:
 *     summary: Manually unblock an IP address
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ip
 *         required: true
 *         schema:
 *           type: string
 *         description: IP address to unblock
 *     responses:
 *       200:
 *         description: IP unblocked successfully
 *       400:
 *         description: Invalid IP address format
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: IP address is not currently blocked
 */
router.delete(
  '/ip/:ip/unblock',
  authMiddleware,
  detectSuspiciousActivity,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  SecurityController.unblockIP,
);

/**
 * @swagger
 * /security/user/trust-score:
 *   get:
 *     summary: Get current user's trust score
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Trust score retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     trustScore:
 *                       type: number
 *                       minimum: 0
 *                       maximum: 1
 *                     trustLevel:
 *                       type: string
 *                       enum: [VERY_LOW, LOW, MEDIUM, HIGH]
 *       401:
 *         description: Unauthorized
 */
router.get(
  '/user/trust-score',
  authMiddleware,
  AdvancedRateLimitMiddleware.general(),
  SecurityController.getUserTrustScore,
);

/**
 * @swagger
 * /security/cleanup:
 *   post:
 *     summary: Trigger cleanup of old security data
 *     tags: [Security Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/cleanup',
  authMiddleware,
  detectSuspiciousActivity,
  AdvancedRateLimitMiddleware.sensitiveOperation(),
  SecurityController.cleanupSecurityData,
);

export default router;
