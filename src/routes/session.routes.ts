import { Router } from 'express';
import { <PERSON><PERSON><PERSON>roller } from '../controllers/session.controller';
import { authMiddleware, detectSuspiciousActivity, rateLimiterMiddleware, validationMiddleware } from '@/middlewares';
import { TerminateSessionDto, TrustDeviceDto, TerminateAllSessionsDto } from '../dtos/session.dto';

const router = Router();

/**
 * @swagger
 * /sessions:
 *   get:
 *     summary: Get all sessions for the authenticated user
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Sessions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           deviceId:
 *                             type: string
 *                           deviceName:
 *                             type: string
 *                           deviceType:
 *                             type: string
 *                           userAgent:
 *                             type: string
 *                           ipAddress:
 *                             type: string
 *                           location:
 *                             type: string
 *                           isActive:
 *                             type: boolean
 *                           isTrusted:
 *                             type: boolean
 *                           lastActivity:
 *                             type: string
 *                             format: date-time
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           expiresAt:
 *                             type: string
 *                             format: date-time
 *                           riskScore:
 *                             type: number
 *                           suspiciousActivity:
 *                             type: boolean
 *                     totalSessions:
 *                       type: number
 *                     activeSessions:
 *                       type: number
 *                     trustedDevices:
 *                       type: number
 *       401:
 *         description: Unauthorized
 */
router.get('/', authMiddleware, SessionController.getSessions);

/**
 * @swagger
 * /sessions/current:
 *   get:
 *     summary: Get current session information
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current session information retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.get('/current', authMiddleware, SessionController.getCurrentSession);

/**
 * @swagger
 * /sessions/terminate:
 *   post:
 *     summary: Terminate a specific session
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sessionId:
 *                 type: string
 *                 format: uuid
 *               reason:
 *                 type: string
 *             required:
 *               - sessionId
 *     responses:
 *       200:
 *         description: Session terminated successfully
 *       400:
 *         description: Invalid session ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Session not found
 */
router.post(
  '/terminate',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(TerminateSessionDto),
  SessionController.terminateSession,
);

/**
 * @swagger
 * /sessions/terminate-all-others:
 *   post:
 *     summary: Terminate all other sessions (keep current session active)
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: All other sessions terminated successfully
 *       401:
 *         description: Unauthorized
 */
router.post(
  '/terminate-all-others',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(TerminateAllSessionsDto),
  SessionController.terminateAllSessions,
);

/**
 * @swagger
 * /sessions/logout:
 *   post:
 *     summary: Logout current session
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logged out successfully
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', authMiddleware, detectSuspiciousActivity, rateLimiterMiddleware, SessionController.logout);

/**
 * @swagger
 * /sessions/trust-device:
 *   post:
 *     summary: Trust a device
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               deviceId:
 *                 type: string
 *             required:
 *               - deviceId
 *     responses:
 *       200:
 *         description: Device trusted successfully
 *       400:
 *         description: Invalid device ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Device not found
 */
router.post(
  '/trust-device',
  authMiddleware,
  detectSuspiciousActivity,
  rateLimiterMiddleware,
  validationMiddleware(TrustDeviceDto),
  SessionController.trustDevice,
);

/**
 * @swagger
 * /sessions/activity:
 *   get:
 *     summary: Get session activity for the user
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: string
 *         description: Number of activities to return (max 100)
 *       - in: query
 *         name: offset
 *         schema:
 *           type: string
 *         description: Number of activities to skip
 *     responses:
 *       200:
 *         description: Session activity retrieved successfully
 *       400:
 *         description: Invalid query parameters
 *       401:
 *         description: Unauthorized
 */
router.get('/activity', authMiddleware, SessionController.getSessionActivity);

/**
 * @swagger
 * /sessions/trusted-devices:
 *   get:
 *     summary: Get trusted devices for the user
 *     tags: [Session Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Trusted devices retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/trusted-devices', authMiddleware, SessionController.getTrustedDevices);

export default router;
